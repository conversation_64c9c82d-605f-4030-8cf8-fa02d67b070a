# ملف إعدادات محرر مايسترو
# يمكنك تخصيص هذه الإعدادات حسب احتياجاتك

# إعدادات API
MAESTRO_API_PROVIDER = "gemini"  # gemini, openai, claude
<PERSON>_MODEL = "gemini-2.5-flash"
MAESTRO_API_TIMEOUT = 30  # بالثواني

# إعدادات المحرر
MAESTRO_FONT_SIZE = 16
MAESTRO_FONT_FAMILY = "Consolas"
MAESTRO_THEME = "dark"  # dark, light
MAESTRO_AUTO_SAVE = true
MAESTRO_AUTO_SAVE_INTERVAL = 300  # بالثواني (5 دقائق)

# إعدادات المساعد
MAESTRO_CONTEXT_LINES = 50  # عدد الأسطر المحيطة لإرسالها كسياق
MAESTRO_MAX_RESPONSE_LENGTH = 4000  # الحد الأقصى لطول الرد
MAESTRO_AUTO_INSERT_CODE = true  # إدراج الكود تلقائياً
MAESTRO_SHOW_DEBUG_LINKS = true  # إظهار روابط المساعدة عند الأخطاء

# إعدادات Notebook
MAESTRO_NOTEBOOK_AUTO_RUN = false  # تشغيل الخلايا تلقائياً
MAESTRO_NOTEBOOK_SAVE_OUTPUT = true  # حفظ مخرجات الخلايا

# إعدادات التنبيهات
MAESTRO_SHOW_NOTIFICATIONS = true
MAESTRO_NOTIFICATION_DURATION = 3000  # بالميلي ثانية

# إعدادات الأمان
MAESTRO_CONFIRM_CODE_EXECUTION = false  # طلب تأكيد قبل تنفيذ الكود
MAESTRO_SANDBOX_MODE = false  # وضع الحماية (قيد التطوير)

# إعدادات التخزين
MAESTRO_HISTORY_MAX_SIZE = 100  # الحد الأقصى للمحادثات المحفوظة
MAESTRO_AUTO_BACKUP = true
MAESTRO_BACKUP_INTERVAL = 1800  # بالثواني (30 دقيقة)

# إعدادات الواجهة
MAESTRO_SHOW_LINE_NUMBERS = true
MAESTRO_HIGHLIGHT_CURRENT_LINE = true
MAESTRO_WORD_WRAP = false
MAESTRO_SHOW_WHITESPACE = false

# إعدادات الاختصارات
MAESTRO_SHORTCUTS = [
    ["Ctrl+Shift+A", "sendToAssistant()"],
    ["Ctrl+Shift+E", "explainCode()"],
    ["Ctrl+Shift+D", "debugCode()"],
    ["Ctrl+Shift+R", "refactorCode()"],
    ["F5", "runCode()"],
    ["Ctrl+S", "saveFile()"],
    ["Ctrl+O", "openFile()"],
    ["Ctrl+N", "newFile()"]
]

# دالة تحميل الإعدادات
func loadMaestroConfig
    # يمكن تطوير هذه الدالة لتحميل الإعدادات من ملف JSON أو INI
    return true

# دالة حفظ الإعدادات
func saveMaestroConfig
    # يمكن تطوير هذه الدالة لحفظ الإعدادات
    return true

# دالة تطبيق الإعدادات على المحرر
func applyMaestroConfig
    # تطبيق إعدادات الخط
    if isDefined("oCodeEditor")
        oCodeEditor.setFont(new QFont(MAESTRO_FONT_FAMILY, MAESTRO_FONT_SIZE))
    ok
    
    # تطبيق إعدادات المظهر
    if MAESTRO_THEME = "dark"
        # تطبيق المظهر الداكن
    else
        # تطبيق المظهر الفاتح
    ok
    
    return true
