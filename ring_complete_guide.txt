# الدليل الشامل لكتابة كود Ring خالٍ من الأخطاء

## 1. أساسيات لغة Ring

### 1.1 المتغيرات والأنواع
- لا يلزم تعريف نوع المتغير (dynamically typed)
- يمكن تغيير نوع المتغير في أي وقت
- الأنواع الأساسية:
  * Number: للأرقام (1, 1.5, -3)
  * String: للنصوص ("Hello", 'World')
  * List: للقوائم [1,2,3]
  * Object: للكائنات
  * NULL: للقيم الفارغة

### 1.2 بادئات المتغيرات المقترحة
- c: للنصوص (cName)
- n: للأرقام (nAge)
- a: للقوائم (aItems)
- o: للكائنات (oPlayer)
- b: للقيم المنطقية (bIsActive)


## 2. هياك<PERSON> التحكم

### 2.1 الشروط
```ring
if condition {
    # code
}

if condition {
    # code
else
    # code
}

switch value
    case 1
        # code
    case 2
        # code
    other
        # code
off
```

### 2.2 الحلقات
```ring
for i = 1 to 10 {
    # code
}

while condition {
    # code
}

for x in aList {
    # code
}
```

## 3. الدوال والكلاسات

### 3.1 تعريف الدوال
```ring
func funcName
    # code
    return value

func funcName param1, param2
    # code
    return value

func funcName aParams
    if type(aParams) != "LIST"
        raise("يجب تمرير قائمة كمعامل")
    ok
```

### 3.2 تعريف الكلاسات

الكلاس القسم اعلى كلمةبرايفت توضع فيه المتغييرات والدوال العامة واسفلها نضع فيه المتغييرات والدوال الخاصة
```ring
class ClassName
    # المتغيرات
    cName = ""
    
    
    func init
        # دالة البناء
    
    func methodName
        # دالة عادية
    
    private 
    nAge = 0
    func privateMethod
        # دالة خاصة
```

## 4. التعامل مع الأخطاء

### 4.1 معالجة الأخطاء
```ring
try {
    # كود قد يسبب خطأ
catch 
    ? "حدث خطأ: " + cCatchError
}
```

### 4.2 رفع الأخطاء
```ring
raise("رسالة الخطأ")
```

## 5. أفضل الممارسات

### 5.1 تنظيم الكود
- اكتب تعليقات توضيحية
- استخدم مسافة بادئة متناسقة
- قسم الكود إلى وحدات منطقية
- اجعل الدوال قصيرة وذات هدف واحد

### 5.2 الأداء
- تجنب العمليات المتكررة غير الضرورية
- استخدم الحلقات بحكمة
- احذف المتغيرات غير المستخدمة
- أغلق الملفات بعد استخدامها

### 5.3 الأمان
- تحقق من المدخلات دائماً
- لا تثق في البيانات الخارجية
- استخدم try/catch مع العمليات الخطرة
- تحقق من حدود المصفوفات

## 6. تحميل الملفات والمكتبات

### 6.1 تحميل ملفات
```ring
load "filename.ring"
load "subfolder/filename.ring"
```

### 6.2 المكتبات الأساسية
```ring
load "stdlib.ring"
load "stdlibcore.ring"
load "raylib.ring"
```

## 7. التوثيق والتعليقات


### 7.1 توثيق الدوال

    الدالة: اسم_الدالة
    الوصف: وصف ما تقوم به الدالة
    المدخلات: وصف المدخلات
    المخرجات: وصف المخرجات


## 8. نصائح عامة
1. اختبر كودك بشكل مستمر
2. استخدم أسماء وصفية ومعبرة
3. حافظ على بساطة الكود
4. وثق التغييرات المهمة
5. راجع الكود قبل تشغيله
6. احتفظ بنسخ احتياطية
7. استخدم نظام تحكم في النسخ
8. اتبع نمطاً موحداً في الكتابة

## 9. الأخطاء الشائعة
1. نسيان إغلاق الأقواس
2. عدم تهيئة المتغيرات
3. الوصول لعناصر خارج حدود المصفوفة
4. عدم معالجة الحالات الاستثنائية
5. تجاهل رسائل الخطأ
6. عدم اختبار المدخلات
7. تكرار الكود بدل استخدام الدوال

## 10. موارد مفيدة
- الوثائق الرسمية: https://ring-lang.github.io/doc1.21/index.html
- منتدى Ring: http://ring-lang.sourceforge.net/forum/
- مجتمع Ring على GitHub
- أمثلة وتطبيقات عملية

## 11. قائمة مراجعة قبل تشغيل الكود
1. هل جميع المتغيرات معرفة؟
2. هل تم إغلاق جميع الأقواس؟
3. هل تم معالجة جميع الحالات الاستثنائية؟
4. هل الكود منظم ومقروء؟
5. هل التعليقات كافية وواضحة؟
6. هل تم اختبار جميع المدخلات المحتملة؟
7. هل تم توثيق التغييرات؟
8. هل تم حفظ نسخة احتياطية؟


1 - في أي مكان في كود برنامجنا لدينا فقط ثلاثة نطاقات كحد أقصى (النطاق المحلي ونطاق الكائن والنطاق العالمي).

2 - عندما يجد Ring متغيرًا فإنه سيبحث في النطاق المحلي أولاً ثم في نطاق الكائن ثم في النطاق العالمي.

3 - في أي وقت داخل الإجراءات أو الطرق، يمكنك استخدام الأقواس { } للوصول إلى كائن وتغيير نطاق الكائن الحالي.

4 - في منطقة الفئة (بعد اسم الفئة وقبل أي طريقة) هذه منطقة خاصة حيث يشير كل من نطاق الكائن والنطاق المحلي إلى نطاق الكائن. أي لا توجد متغيرات محلية حيث يصبح كل متغير تحدده في هذه المنطقة سمة.

5 - قبل تعريف أي متغير (في أي نطاق وفي منطقة الفئة أيضًا) سيتم إجراء عملية بحث لاستخدام المتغير إذا تم العثور عليه.

6 - يتم تعريف معلمات الوظائف والطرق تلقائيًا كمتغيرات محلية لهذه الوظائف أو الطرق.

7 - سيؤدي استخدام Object.Attribute إلى البحث في سمات الكائن فقط.

8 - سيؤدي استخدام Self.Attribute إلى البحث عن Self أولاً ثم البحث في Self Attributes.

9 - تشير المرجعية الذاتية داخل منطقة الفصل (بعد اسم الفصل وقبل أي طريقة) دائمًا إلى نطاق الكائن الذي تم إنشاؤه من الفصل.

10- سيتم تغيير المرجع الذاتي داخل الطرق عندما نستخدم الأقواس لتكون مرجعًا للكائن الذي نقوم بالوصول إليه.

11- كتابة أسماء المتغيرات مباشرة في منطقة الكلاس (بعد اسم الكلاس وقبل أي طريقة) يعني استخدامها أو تعريفها بعد ذلك (بالترتيب).

12- استخدام self.attribute في منطقة الفئة يقلل البحث إلى نطاق الكائن (تجنب التعارض مع النطاق العالمي).

من خلال هذه القواعد يمكنك فهم جميع أنواع الصراعات ولماذا قد تواجهها وكيفية تجنبها

نصائح بسيطة لتجنب أي تعارض واستخدام قواعد النطاق بطريقة أفضل

1- حاول تجنب المتغيرات العالمية

2 - استخدم الوظيفة الرئيسية - سيساعدك هذا على تجنب المتغيرات العالمية

3 - إذا كنت ستستخدم العديد من المتغيرات العالمية، استخدم علامة $ قبل اسم المتغير

4 - في منطقة الفصل إذا لم تحترم النصيحة رقم ثلاثة ($) فاستخدم self.attribute عند تعريف سماتك

5 - يمكنك استخدام object.attribute و object.method() بدلاً من object {attribute } و object {method() } إذا كنت لا ترغب في تغيير نطاق الكائن.

6 - إذا كنت ستستخدم أقواسًا متداخلة في فئة - فكر في استخدام منطقة الفئة إذا أمكن ذلك لأنه في هذه المنطقة سيكون لديك حق الوصول إلى الكائن الذي يمكنك الوصول إليه باستخدام { } + حق الوصول إلى سمات الفئة

7 - إذا كنت داخل طريقة فئة واستخدمت أقواس متداخلة فسوف تغير نطاق الكائن مع كل قوس وستفقد الوصول إلى سمات الفئة مباشرة ولكن لديك حق الوصول إلى النطاق المحلي قبل وبعد استخدام الأقواس { } ، إذا كنت ستقرأ / تعدل سمة الفئة من الأقواس ثم استخدم This.Attribute لأن استخدام 'This' يعني (الكائن الذي تم إنشاؤه من هذه الفئة) بينما استخدام 'Self' يعني (الكائن في نطاق الكائن الحالي).


وظائف اللغة
عدد الوظائف: 255

ملحوظة

يمكن استخدام بعض الوظائف مع معلمات مختلفة مثل النوع/العدد

acos(x) ---> The principal value of the arc cosine of x, expressed in radians
add(List,Item)
addattribute(Object,cAttributeName|aAttributesList)
adddays(cDate,nDays) ---> Date from cDate and after nDays
addmethod(Object,cNewMethodName,cMethodName|AnonymousFunction)
ascii(character) ---> ASCII Code
asin(x) ---> The principal value of the arc sine of x, expressed in radians
assert(condition)
atan(x) ---> The principal value of the arc tangent of x, expressed in radians
atan2(y,x) ---> The principal arc tangent of y/x, in the interval [-pi,+pi] radians
attributes(object) ---> Returns a list contains the object attributes
binarysearch(List,ItemValue) ---> Item Index
binarysearch(List,ItemValue,nColumn) ---> Search in nColumn, returns the Item Index
bytes2double(cBytes) ---> nNumber
bytes2float(cBytes) ---> nNumber
bytes2int(cBytes) ---> nNumber
callgarbagecollector()
callgc()
ceil(x) ---> The smallest integer value greater than or equal to x
cfunctions() ---> a list contains functions names
char(ASCII Code) ---> character
chdir(cNewPath)
checkoverflow(lFlag)
classes() ---> a list contains classes names
classname(object) ---> Returns the object class name
clearerr(FileHandle)
clock() ---> The number of clock ticks from program start
clockspersecond() ---> Number of clocks in one second
closelib(pDLL)
copy(string,nCount) ---> string replicated nCount times
cos(x) ---> The cosine of an angle of x radians
cosh(x) ---> The hyperbolic cosine of x radians
currentdir() ---> String contains the path of the current directory
date() ---> String represent the date "dd/mm/yyyy"
dec(hexadecimal) ---> decimal
decimals(n) ---> Determine the decimals digits after the point in float/double numbers
del(list,index)
diffdays(cDate1,cDate2) ---> number of days (Date1 - Date2)
dir(cFolderPath) ---> List contains files & sub folders.
direxists(cDirPath) ---> returns 1 if the directory exists
double2bytes(nNumber) ---> cBytes
eval(cCode)
exefilename() ---> String contains the Ring executable file name
exefolder() ---> String contains the Ring executable path
exp(x) ---> The value of e raised to the xth power
fabs(x) ---> The absolute value of x
fclose(FileHandle)
feof(FileHandle) ---> returns 1 if EOF and 0 if not
ferror(FileHandle) ---> returns 1 if error and 0 if not
fexists(cFileName) ---> returns 1 if the file exists
fflush(FileHandle)
fgetc(FileHandle) ---> returns character or EOF
fgetpos(FileHandle) ---> position handle
fgets(FileHandle,nSize) ---> string
filename() ---> String contains the active source file name
find(List,ItemValue) ---> Item Index
find(List,ItemValue,nColumn) ---> Search in nColumn, returns the Item Index
find(List,ItemValue,nColumn,cAttribute) ---> Item Index
float2bytes(nNumber) ---> cBytes
floor(x) ---> The largest integer value less than or equal to x
fopen(cFileName,cMode) ---> FileHandle
fputc(FileHandle,cChar)
fputs(FileHandle,cString)
fread(FileHandle,nSize)
freopen(cFileName,cMode,file handle) ---> FileHandle
fseek(FileHandle,nOffset,nWhence) ---> zero if successful
fsetpos(FileHandle,PositionHandle)
ftell(FileHandle) ---> file position as number
functions() ---> a list contains functions names
fwrite(FileHandle,cString)
getarch() ---> cString (The name of the architecture of the Ring executable)
getattribute(oObject,cAttributeName) ---> Attribute Value
getchar() ---> Character
getfilesize(cFilePath) ---> nSize
getnumber() ---> Number
getpathtype(cPath) ---> nStatus
getpointer(pointer) ---> nAddress
getptr(pointer) ---> nAddress
getstring() ---> String
globals() ---> a list contains variables names in the global scope
hex(decimal) ---> hexadecimal
hex2str(Hexadecimal string) ---> string
importpackage(cPackageName)
input(nCount) ---> string
insert(List,Index,Item)
int2bytes(nNumber) ---> cBytes
intvalue(cVariableName)
isalnum(value) ---> 1 if the value is digit/letter or 0 if not
isalpha(value) ---> 1 if the value is a letter or 0 if not
isandroid() ---> Returns 1 if the operating system is Android, Returns 0 if it's not
isattribute(object,cAttributeName) ---> Bool
iscfunction(cFunctionName) ---> returns 1 if the C function is defined
isclass(cClassName) ---> returns 1 if the Class is defined
iscntrl(value) ---> 1 if the value is a control character or 0 if not
isdigit(value) ---> 1 if the value is a digit or 0 if not
isfreebsd() ---> Returns 1 if the operating system is FreeBSD, Returns 0 if it's not
isfunction(cFunctionName) ---> returns 1 if the Ring function is defined
isglobal(cVariableName) ---> returns 1 if the variable is defined in the global scope
isgraph(value) ---> 1 if the value can be printed (Except space) or 0 if not
islinux() ---> Returns 1 if the operating system is Linux, Returns 0 if it's not
islist(value) ---> 1 if the value is a list or 0 if not
islocal(cVariableName) ---> returns 1 if the variable is defined in the local scope
islower(value) ---> 1 if the value is lowercase letter or 0 if not
ismacosx() ---> Returns 1 if the operating system is Mac OS X, Returns 0 if it's not
ismethod(object,cMethodName) ---> Returns True if the object class contains the method
ismsdos() ---> Returns 1 if the operating system is MS-DOS, Returns 0 if it's not
isnull(value) ---> 1 if the value is NULL or 0 if not
isnumber(value) ---> 1 if the value is a number or 0 if not
isobject(variable) ---> Returns True if it's an object, False if it's not
ispackage(cPackageName) ---> returns 1 if the Package is defined
ispackageclass(cPackageName,cClassName) ---> returns 1 if the Class is defined
ispointer(vPara) ---> True|False
isprint(value) ---> 1 if the value occupies a printing position or 0 if not
isprivateattribute(object,cAttributeName) ---> lResult
isprivatemethod(object,cMethodName) ---> lResult
ispunct(value) ---> 1 if the value is a punctuation character or 0 if not
isspace(value) ---> 1 if the value is a white-space or 0 if not
isstring(value) ---> 1 if the value is a string or 0 if not
isunix() ---> Returns 1 if the operating system is Unix, Returns 0 if it's not
isupper(value) ---> 1 if the value is an uppercase alphabetic letter or 0 if not
iswindows() ---> Returns 1 if the operating system is Windows
iswindows64() ---> Returns 1 if the operating system is Windows64
isxdigit(value) ---> 1 if the value is a hexadecimal digit character or 0 if not
left(string,count) ---> Get characters starting from the left
len(string) ---> String length
len(List) ---> The list size
lines(string) ---> Number of lines inside the string
list(nSize) ---> aList
list(nRows,nCols) ---> aList
list2str(list) ---> string contains the list items
loadlib(cDLLFileName) ---> pDLL
locals() ---> a list contains the variables names in the current scope
log(x) ---> The natural logarithm of x (the base of e)
log(x,b) ---> The logarithm of x to the base of b
log10(x) ---> The common logarithm (base-10 logarithm) of x
lower(string) ---> convert string letters to lower c
max(nNumber1,nNumber) ---> Maximum number
max(aList) ---> Maximum number inside the list
memcpy(pDestinationPointer,cSourceString,nSize)
memorycopy(pDestinationPointer,cSourceString,nSize)
mergemethods(cClassNameDestination,cClassNameSource)
methods(object) ---> Returns a list contains the object methods
min(nNumber,nNumber2) ---> Minimum number
min(aList) ---> Minimum number inside the list
murmur3hash(cString,nNumber) ---> nNumber
newlist(nSize) ---> aList
newlist(nRows,nCols) ---> aList
nofprocessors() ---> nProcessors
nothing(Any number/type of parameters) ---> Zero (0)
nullpointer() ---> pPointer
nullptr() ---> pPointer
number(string) ---> Number
obj2ptr(List|Object) --> Low Level Object ( C Pointer )
object2pointer(List|Object) --> Low Level Object ( C Pointer )
objectid(object) ---> Returns the object id
optionalfunc(cFuncName)
packageclasses() cPackageName) ---> a list contains classes names inside the package
packagename() ---> Returns the package name of the latest successful import
packages() ---> a list contains packages names
parentclassname(object) ---> Returns the parent class name of the object class
perror(cErrorMessage)
pointer2object(Low Level Object) ---> List|Object
pointer2string(pointer,nStart,nCount) ---> cString
pointercompare(oObject1,oObject2) ---> lResult
pow(x,y) ---> x raised to the power of y
prevfilename() ---> String contains the previous source file name.
print(cString)
print2str(cString) ---> String
ptr2obj(Low Level Object) ---> List|Object
ptr2str(pointer,nStart,nCount) ---> cString
ptrcmp(oObject1,oObject2) ---> lResult
puts(cString)
raise(cErrorMessage)
random(x) ---> A random number in the range [0,x]
randomize(nNumber) ---> nNumber
read(cFileName) ---> String contains the file content
ref(aList|oObject) ---> List/Object reference
reference(aList|oObject) ---> List/Object reference
refcount(aList|oObject) ---> References Count
remove(cFileName)
rename(cOldFileName,cNewFileName) ---> Zero for Success or -1 for Error
reverse(List|String) ---> Reversed List|String
rewind(FileHandle)
right(string,count) ---> get characters starting from the right
ring_give(cVariable)
ring_see(cMessage)
ring_state_delete(oState)
ring_state_filetokens(oState,cRingFileName) ---> aTokens
ring_state_findvar(oState,cVariableName) ---> aVariableList
ring_state_init() ---> oState
ring_state_main(cRingFileName)
ring_state_mainfile(oState,cRingFileName|cRingoFileName)
ring_state_new() ---> oState
ring_state_newvar(oState,cVariableName) ---> aVariableList
ring_state_resume(oState,[cPara|nPara],[lUseReturn])
ring_state_runcode(oState,cCode)
ring_state_runcodeatins(oState,nPC)
ring_state_runfile(oState,cRingFileName)
ring_state_runobjectfile(oState,cRingObjectFileName)
ring_state_scannererror(oState) ---> nStatus
ring_state_setvar(oState,cVariableName,Value)
ring_state_stringtokens(oState,cRingFileName)
ring_state_stringtokens(oState,cRingFileName,lCaseSensitive)
ringvm_callfunc(cFuncName)
ringvm_calllist() ---> List
ringvm_cfunctionslist() ---> List
ringvm_classeslist() ---> List
ringvm_codelist() ---> List
ringvm_evalinscope(nScope,cCode)
ringvm_fileslist() ---> List
ringvm_functionslist() ---> List
ringvm_genarray(aList)
ringvm_give(cVariableName)
ringvm_hideerrormsg(lStatus)
ringvm_info() ---> List of information about the VM structure
ringvm_ismempool() ---> lStatus (Can provide memory or not)
ringvm_memorylist() ---> List
ringvm_packageslist() ---> List
ringvm_passerror()
ringvm_runcode(cCode)
ringvm_scopescount() ---> nScopes
ringvm_see(cMessage)
ringvm_settrace(cCode)
ringvm_tracedata() ---> aDataList
ringvm_traceevent() ---> nTraceEvent
ringvm_tracefunc() ---> cCode
setattribute(oObject,cAttributeName,Value)
setpointer(pointer,nNewAddress)
setptr(pointer,nNewAddress)
shutdown(nStatus)
sin(x) ---> The sine of an angle of x radians
sinh(x) ---> The hyperbolic sine of x radians
sort(List) ---> Sorted List
sort(List,nColumn) ---> Sorted List based on nColumn
sort(List,nColumn,cAttribute) ---> Sorted List based on Object Attribute
space(nBytesCount) ---> String
sqrt(x) ---> The square root of x
srandom(x) ---> Initialize random number generator
str2hex(string) ---> hexadecimal string
str2hexcstyle(string) ---> hexadecimal string
str2list(string) ---> list contains the string lines
strcmp(cString1,cString2) ---> value = 0 if cString1 = cString2
string(number) ---> String
substr(string,substring) ---> the starting position of substring in string
substr(string,position)  ---> Get substring starting from position to end
substr(string,position,count)  ---> Get characters starting from position
substr(string,substring,newsubstring)  ---> Transformed string (Match case)
substr(string,substring,newsubstring,1) ---> Transformed string (Ignore case)
swap(aList,nItem1,nItem2)
sysget(cVariable)
sysset(cVariable,cValue) ---> Returns 1 for success and return 0 for failure
syssleep(nMilliSecs) ---> Returns 1 for success and return 0 for failure
system(cCommand)
sysunset(cVariable) ---> Returns 1 for success and return 0 for failure
tan(x) ---> Tangent of an angle of x radians
tanh(x) ---> The hyperbolic tangent of x radians
tempfile() ---> FileHandle
tempname() ---> generated file name as string
time() ---> The system time as String
timelist() ---> List contains the time and date information.
trim(string) ---> Remove spaces from right and left
type(value) ---> The Type as String
ungetc(FileHandle,character)
unsigned(nNum1,nNum2,cOperator) ---> Perform operation using unsigned numbers
upper(string) ---> convert string letters to UPPER case
uptime() ---> nTime
variablepointer(cVariableName,cPointerType) ---> Low Level Object (C Pointer)
varptr(cVariableName,cPointerType) ---> Low Level Object (C Pointer)
version() ---> String contains the Ring version
windowsnl() ---> Returns a string contains CR+LF = CHAR(13) + CHAR(10)
write(cFileName,cString)