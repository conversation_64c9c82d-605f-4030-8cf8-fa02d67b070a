/*
===========================================
فئة محرر الكود المخصص لمايسترو
===========================================
الوصف: محرر كود متقدم مع قائمة سياق ذكية
المميزات:
- قائمة سياق بزر الماوس الأيمن
- إرسال الكود للمساعد مع السياق الكامل
- دعم الشرح والتصحيح وإعادة الهيكلة
===========================================
*/

load "guilib.ring"

class MaestroCodeEditor from CodeEditor
    
    func init oParent
        super.init(oParent)
        return self

    func contextMenuEvent oEvent
        # إنشاء القائمة الأساسية
        oMenu = createStandardContextMenu()
        oMenu.addSeparator()
        
        # إضافة خيارات المساعد الذكي
        oActionExplain = oMenu.addAction("🧠 المساعد: اشرح هذا الكود")
        oActionFindBugs = oMenu.addAction("🐛 المساعد: ابحث عن أخطاء")
        oActionRefactor = oMenu.addAction("⚡ المساعد: أعد هيكلة هذا الكود")
        oActionComplete = oMenu.addAction("✨ المساعد: أكمل هذا الكود")
        oActionOptimize = oMenu.addAction("🚀 المساعد: حسّن هذا الكود")
        
        # عرض القائمة والحصول على الاختيار
        oSelectedAction = oMenu.exec(self.mapToGlobal(oEvent.pos()))
        
        # التحقق من وجود نص محدد
        cSelectedText = self.textCursor().selectedText()
        if cSelectedText = "" 
            # إذا لم يكن هناك نص محدد، استخدم السطر الحالي
            oCursor = self.textCursor()
            oCursor.select(QTextCursor_LineUnderCursor)
            cSelectedText = oCursor.selectedText()
            if cSelectedText = ""
                return
            ok
        ok
        
        # تنفيذ الإجراء المحدد
        if oSelectedAction = oActionExplain
            cTask = "اشرح الكود التالي بالتفصيل وبشكل مبسط:"
            sendCodeToAssistant(cTask, cSelectedText)
        elseif oSelectedAction = oActionFindBugs
            cTask = "ابحث عن أي أخطاء منطقية أو مشاكل محتملة في الكود التالي واقترح الإصلاحات:"
            sendCodeToAssistant(cTask, cSelectedText)
        elseif oSelectedAction = oActionRefactor
            cTask = "قم بتحسين وإعادة هيكلة الكود التالي ليكون أكثر كفاءة وقراءة:"
            sendCodeToAssistant(cTask, cSelectedText)
        elseif oSelectedAction = oActionComplete
            cTask = "أكمل الكود التالي بناءً على السياق المتاح:"
            sendCodeToAssistant(cTask, cSelectedText)
        elseif oSelectedAction = oActionOptimize
            cTask = "حسّن الكود التالي من ناحية الأداء والذاكرة:"
            sendCodeToAssistant(cTask, cSelectedText)
        ok


