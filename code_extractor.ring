class CodeExtractor
    # الخصائص
    cText = ""        # النص الكامل للرد
    aCodeBlocks = []  # مصفوفة لتخزين كتل الكود
    aComments = []    # مصفوفة لتخزين التعليقات
    aLanguages = []   # مصفوفة لتخزين لغات البرمجة لكل كتلة
    cStrCode = ""     # نص كتلة الكود

    # Constructor
    func init cResponseText
        cText = cResponseText
        extractCodeBlocks()
        return self

    # استخراج كتل الكود من النص
    func extractCodeBlocks
        aCodeBlocks = []  # إعادة تهيئة المصفوفات
        aComments = []
        aLanguages = []
        
        aLines = str2list(cText)
        isCodeBlock = false
        cCurrentBlock = ""
        cCurrentComment = ""
        
        for i = 1 to len(aLines)
            cLine = aLines[i]
            
            # البحث عن بداية كتلة الكود
            if substr(cLine, "```") = 1
                if !isCodeBlock
                    # حفظ التعليق السابق إذا وجد
                    if trim(cCurrentComment) != ""
                        add(aComments, cCurrentComment)
                        cCurrentComment = ""
                    ok
                    
                    # استخراج لغة البرمجة
                    cLang = substr(cLine, 4)
                    cLang = trim(cLang)
                    add(aLanguages, cLang)
                    isCodeBlock = true
                else
                    # نهاية كتلة الكود
                    if trim(cCurrentBlock) != ""
                        add(aCodeBlocks, cCurrentBlock)
                    ok
                    cCurrentBlock = ""
                    isCodeBlock = false
                ok
                loop
            ok
            
            # تجميع الكود أو التعليقات
            if isCodeBlock
                if cCurrentBlock != "" cCurrentBlock += nl ok
                cCurrentBlock += cLine
            else
                if cLine != ""
                    if cCurrentComment != "" cCurrentComment += nl ok
                    cCurrentComment += "# " + cLine
                ok
            ok
        next
        
        # إضافة آخر كتلة إذا كانت موجودة
        if trim(cCurrentBlock) != ""
            add(aCodeBlocks, cCurrentBlock)
        ok
        if trim(cCurrentComment) != ""
            add(aComments, cCurrentComment)
        ok
        
        # تحضير النص النهائي
        cStrCode = formatExtractedCode()
        return self

    # تنسيق الكود المستخرج
    func formatExtractedCode
        cResult = ""
        
        for i = 1 to len(aCodeBlocks)
            # إضافة التعليق إذا وجد
            if i <= len(aComments)
                cResult += aComments[i] + nl + nl
            ok
            
            # إضافة لغة البرمجة كتعليق
            if i <= len(aLanguages) and aLanguages[i] != ""
                cResult += "# Language: " + aLanguages[i] + nl
            ok
            
            # إضافة الكود
            cResult += aCodeBlocks[i] + nl + nl
        next
        
        # إضافة التعليقات المتبقية
        for i = len(aCodeBlocks) + 1 to len(aComments)
            cResult += aComments[i] + nl + nl
        next
        
        return trim(cResult)

    # الحصول على الكود المستخرج
    func getExtractedCode
        return cStrCode

    # الحصول على النص الأصلي
    func getOriginalText
        return cText

    # تنظيف سطر الكود من العلامات الخاصة
    func cleanCodeLine cLine
        cResult = cLine
        
        # إزالة علامات التعليقات
        if substr(cResult, "# =====") > 0
            return ""
        ok
        
        # تنظيف علامات التنصيص والباك سلاش
        cResult = substr(cResult, '\"', '"')      # تحويل \" إلى "
        cResult = substr(cResult, "\\", "")       # إزالة الباك سلاش الزائد
        cResult = substr(cResult, "\t", "   ")   # تحويل التاب إلى مسافات
        
        # تنظيف علامات التوثيق
        if substr(cResult, "'''") > 0 or substr(cResult, '"""') > 0
            return ""
        ok
        
        # إزالة التعليقات في نهاية السطر
        if substr(cResult, "#") > 0
            nPos = substr(cResult, "#")
            if nPos > 1
                cResult = left(cResult, nPos - 1)
            else
                return ""
            ok
        ok
        
        return trim(cResult)
    
    # معالجة الرموز المشوهة
    func cleanSpecialChars cText
        # إزالة علامات الاقتباس
        cText = substr(cText, char(34), "")
        
        # معالجة الأحرف الخاصة
        cText = substr(cText, "\u003c", "<")
        cText = substr(cText, "\u003e", ">")
        
        # إزالة علامات التنسيق
        cText = substr(cText, "[CursorSurroundingLines]", "")
        cText = substr(cText, "[StartOfDocument .]", "")
        cText = substr(cText, "[EndOfDocument .]", "")
        
        return cText
    
    # تنظيف النص من علامات HTML وترميز Unicode
    func cleanHtmlTags cText
        cResult = cText
        
        # تحويل التعليقات إلى تنسيق Ring
        for comment in aComments
            if substr(comment, "#") != 1
                comment = "# " + comment
            ok
        next
        
        # معالجة علامات التنصيص والرموز الخاصة
        cResult = substr(cResult, "\'", "'")     # توحيد علامات التنصيص المفردة
        cResult = substr(cResult, '\"', '"')     # توحيد علامات التنصيص المزدوجة
        cResult = substr(cResult, "\\", "/")     # تحويل الباك سلاش إلى سلاش
        cResult = substr(cResult, "\t", "   ")   # تحويل التاب إلى مسافات
        
        # إزالة علامات HTML
        cResult = substr(cResult, "<div>", "")
        cResult = substr(cResult, "</div>", "")
        cResult = substr(cResult, "<br>", nl)
        cResult = substr(cResult, "<p>", "")
        cResult = substr(cResult, "</p>", nl)
        cResult = substr(cResult, "&nbsp;", " ")
        
        # معالجة علامات السطر الجديد
        cResult = substr(cResult, "\n", nl)
        cResult = substr(cResult, "\r", "")
        
        # معالجة علامات Markdown
        cResult = substr(cResult, "**", "")
        cResult = substr(cResult, "*", "")
        
        # إزالة التعليقات في نهاية السطر
        if substr(cResult, "//") > 0
            nPos = substr(cResult, "//")
            if nPos > 1
                cResult = left(cResult, nPos - 1)
            else
                return ""
            ok
        ok
        
        # إزالة الفراغات المتكررة
        while substr(cResult, nl+nl+nl)
            cResult = substr(cResult, nl+nl+nl, nl+nl)
        end
        
        return trim(cResult)
    
    # نسخ إلى الحافظة
    func copyToClipboard cCode
        oApp = new QApp
        oClip = oApp.clipboard()
        oClip.setText(cCode)
        return "تم النسخ إلى الحافظة"
    
    # تحليل نوع الكود
    func analyzeCodeType cCode
        # تحليل لغة Ring
        if substr(cCode, "class ") > 0
            return "تعريف صنف"
        elseif substr(cCode, "func ") > 0
            return "تعريف دالة"
        elseif substr(cCode, "=") > 0
            return "تعيين متغير"
        # تحليل HTML
        elseif substr(cCode, "<html") > 0 or substr(cCode, "<!DOCTYPE") > 0
            return "HTML"
        # تحليل CSS
        elseif substr(cCode, "{") > 0 and substr(cCode, "}") > 0 and substr(cCode, ":") > 0
            return "CSS"
        # تحليل JavaScript
        elseif substr(cCode, "function") > 0 or substr(cCode, "var ") > 0 or substr(cCode, "let ") > 0
            return "JavaScript"
        # تحليل Python
        elseif substr(cCode, "def ") > 0 or substr(cCode, "class ") > 0 or substr(cCode, "=") > 0
            return "Python"
        else
            return "كود عام"
        ok
    
    # تلوين الكود في النص
    func highlightCode cText
        cResult = cText
        for i = 1 to len(aCodeBlocks)
            cCode = aCodeBlocks[i]
            cLang = ""
            if i <= len(aLanguages)
                cLang = aLanguages[i]
            ok
            
            cStyle = "background-color: #f8f9fa;"
            if cLang = "ring"
                cStyle += "color: #0066cc;"
            elseif cLang = "html"
                cStyle += "color: #e34c26;"
            elseif cLang = "css"
                cStyle += "color: #563d7c;"
            elseif cLang = "javascript"
                cStyle += "color: #f1e05a;"
            elseif cLang = "python"
                cStyle += "color: #3572A5;"
            ok
            
            cColored = "```" + cLang + nl + 
                      "<div style='" + cStyle + " padding: 14px; border-radius: 4px;'>" +
                      cCode +
                      "</div>" +
                      "```"
            cResult = substr(cResult, cCode, cColored)
        next
        return cResult
    
    # إنشاء أزرار للكود
    func createCodeButtons oParent, cCode, nX, nY, cLanguage
        # تخزين الكود في متغير عام مؤقت للوصول إليه من الدوال
        cCurrentCode = cCode
        
        # زر النسخ
        btnCopy = new qPushButton(oParent) {
            setGeometry(nX, nY, 30, 30)
            settext("📋")
            setStyleSheet("
                QPushButton { 
                    background-color: #27ae60;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    font-size: 14px;
                }
                QPushButton:hover { 
                    background-color: #2ecc71;
                }
            ")
            setToolTip("نسخ الكود")
            setclickevent("CodeExtractor.handleCopy()")
        }
        
        # زر التنفيذ - فقط للغات المدعومة
        if cLanguage = "ring"
            btnRun = new qPushButton(oParent) {
                setGeometry(nX + 35, nY, 30, 30)
                settext("⚡")
                setStyleSheet("
                    QPushButton { 
                        background-color: #2980b9;
                        color: white;
                        border: none;
                        border-radius: 4px;
                        font-size: 14px;
                    }
                    QPushButton:hover { 
                        background-color: #3498db;
                    }
                ")
                setToolTip("تنفيذ الكود")
                setclickevent("CodeExtractor.handleRun()")
            }
        ok
        
        # زر التحليل
        btnAnalyze = new qPushButton(oParent) {
            setGeometry(nX + 70, nY, 30, 30)
            settext("🔍")
            setStyleSheet("
                QPushButton { 
                    background-color: #8e44ad;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    font-size: 14px;
                }
                QPushButton:hover { 
                    background-color: #9b59b6;
                }
            ")
            setToolTip("تحليل الكود")
            setclickevent("handleAnalyze()")
        }
    
    # معالجة النقر على زر النسخ
    func handleCopy
        copyToClipboard(cCurrentCode)
    
    # معالجة النقر على زر التنفيذ
    func handleRun
        
    # معالجة النقر على زر التحليل
    func handleAnalyze
        cType = analyzeCodeType(cCurrentCode)
        //status1.showmessage("نوع الكود: " + cType, 0)
    
    # إضافة أزرار لجميع كتل الكود في النص
    func addCodeButtons oParent
        nY = 10
        for i = 1 to len(aCodeBlocks)
            cCode = aCodeBlocks[i]
            cLang = ""
            if i <= len(aLanguages)
                cLang = aLanguages[i]
            ok
            createCodeButtons(oParent, cCode, 10, nY, cLang)
            nY += 40
        next
