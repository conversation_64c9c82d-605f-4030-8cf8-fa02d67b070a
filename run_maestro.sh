#!/bin/bash

echo "تشغيل محرر مايسترو الذكي..."
echo "================================"

# التحقق من وجود Ring
if ! command -v ring &> /dev/null; then
    echo "خطأ: لم يتم العثور على Ring Programming Language"
    echo "يرجى تثبيت Ring أولاً من: http://ring-lang.net"
    exit 1
fi

# التحقق من وجود الملفات المطلوبة
if [ ! -f "smart_editor.ring" ]; then
    echo "خطأ: لم يتم العثور على ملف smart_editor.ring"
    exit 1
fi

if [ ! -f "maestro_prompt.txt" ]; then
    echo "تحذير: لم يتم العثور على ملف maestro_prompt.txt"
    echo "سيتم إنشاؤه تلقائياً..."
fi

echo "تشغيل المحرر..."
ring smart_editor.ring
