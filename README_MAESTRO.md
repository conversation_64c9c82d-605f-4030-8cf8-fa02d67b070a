# محرر مايسترو الذكي - الإصدار 2.0

## نظرة عامة
محرر مايسترو هو بيئة تطوير متكاملة وذكية للغة Ring مع مساعد برمجة متقدم يستخدم تقنيات الذكاء الاصطناعي.

## الميزات الجديدة في الإصدار 2.0

### 1. المساعد الذكي المتطور
- **موجه نظامي متخصص**: يفهم المساعد دوره كخبير في لغة Ring
- **تحليل السياق**: يحلل الكود المحيط لتقديم إجابات أكثر دقة
- **تنسيق مخرجات ذكي**: يستخدم علامات مخصصة لتوجيه المخرجات

### 2. قائمة السياق الذكية
انقر بزر الماوس الأيمن على أي كود للحصول على:
- 🧠 **شرح الكود**: شرح مفصل ومبسط
- 🐛 **البحث عن أخطاء**: تحليل المشاكل المحتملة
- ⚡ **إعادة الهيكلة**: تحسين بنية الكود
- ✨ **إكمال الكود**: إكمال الكود الناقص
- 🚀 **تحسين الكود**: تحسين الأداء

### 3. إدراج الكود الذكي
- الكود المولد من المساعد يُدرج تلقائياً في موضع المؤشر
- دعم العلامات المخصصة `[MAESTRO_CODE_START]` و `[MAESTRO_CODE_END]`

### 4. مساعد الأخطاء الاستباقي
- عند حدوث خطأ في التشغيل، يظهر رابط "اسأل المساعد عن هذا الخطأ"
- النقر على الرابط يرسل الخطأ والكود للمساعد تلقائياً

### 5. الخلايا السحرية في Notebook
استخدم الأمر `%%gemini` في خلايا Notebook للتحدث مع المساعد:
```
%%gemini اكتب دالة لحساب المضروب
```

## كيفية الاستخدام

### الإعداد الأولي
1. تأكد من وجود ملف `maestro_prompt.txt` في مجلد المشروع
2. ضع مفتاح Gemini API الخاص بك في المتغير `GEMINI_API_KEY`
3. شغل المحرر: `ring smart_editor.ring`

### استخدام قائمة السياق
1. حدد أي جزء من الكود
2. انقر بزر الماوس الأيمن
3. اختر الإجراء المطلوب من قائمة المساعد

### استخدام الخلايا السحرية
1. اذهب إلى محرر Notebook (زر "تبديل المحرر")
2. أضف خلية جديدة
3. اكتب `%%gemini` متبوعاً بطلبك
4. شغل الخلية

### التعامل مع الأخطاء
1. شغل كود به خطأ
2. انظر لنافذة الناتج
3. انقر على رابط "اسأل المساعد عن هذا الخطأ"

## العلامات المخصصة للمساعد

المساعد يستخدم علامات خاصة لتنظيم مخرجاته:

### للكود:
```
[MAESTRO_CODE_START]
# الكود هنا
[MAESTRO_CODE_END]
```

### للشرح:
```
[MAESTRO_EXPLANATION_START]
## الشرح هنا بتنسيق Markdown
[MAESTRO_EXPLANATION_END]
```

### لتقارير الأخطاء:
```
[MAESTRO_DEBUG_START]
[{"line": "5", "issue": "المشكلة", "suggestion": "الحل"}]
[MAESTRO_DEBUG_END]
```

## ملفات المشروع

- `smart_editor.ring`: الملف الرئيسي للمحرر
- `MaestroCodeEditor.ring`: فئة محرر الكود المخصص
- `Code_Extractor.ring`: فئة استخراج وتحليل الكود
- `maestro_prompt.txt`: الموجه النظامي للمساعد
- `test_maestro.ring`: ملف اختبار للميزات الجديدة

## نصائح للاستخدام الأمثل

1. **استخدم السياق**: حدد الكود ذي الصلة قبل طلب المساعدة
2. **كن محدداً**: اطلب مهام واضحة (شرح، تصحيح، إلخ)
3. **استفد من الخلايا السحرية**: مثالية للتجارب السريعة
4. **راجع الأخطاء**: استخدم رابط المساعدة عند حدوث أخطاء

## المتطلبات

- Ring Programming Language
- اتصال بالإنترنت (لـ Gemini API)
- مفتاح Gemini API صالح
- نظام التشغيل: Windows/Linux/macOS

## الدعم والمساهمة

هذا المحرر مفتوح المصدر ويرحب بالمساهمات. لأي استفسارات أو اقتراحات، يرجى فتح issue في المستودع.
