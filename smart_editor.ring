/*
===========================================
المحرر الذكي - نسخة 1.0
===========================================
الوصف: محرر نصوص ذكي مع مساعد برمجة مدمج
المميزات:
- محادثة ذكية مع المساعد
- محرر نصوص متقدم
- دعم للغة Ring
- حفظ وتحميل المحادثات
- تغيير المظهر (ليلي/نهاري)
===========================================
*/

load "guilib.ring"
load "jsonlib.ring"
load "Code_Extractor.ring"
load "stdlib.ring"
# =========== تعريف المتغيرات العامة ===========
/*
نستخدم البادئات التالية:
o: للكائنات
c: للنصوص
n: للأرقام
a: للمصفوفات
b: للقيم المنطقية
*/



status1 = NULL            # شريط الحالة
oOutputWindow = NULL       # نافذة عرض المخرجات
oChatEditor = NULL        # محرر المحادثة الحالية
oCodeEditor = NULL        # محرر الكود الأساسي
oNotebookEditor = NULL   # محرر ثانوي
oChatHistoryEditor = NULL # محرر سجل المحادثات
oFileList = NULL         # قائمة الملفات
cCurrentFileName = NULL   # اسم الملف النشط
aChatMessages = []        # مصفوفة لتخزين رسائل المحادثة الحالية
aChatHistory = []        # مصفوفة لتخزين سجل المحادثات
aHistoryFrames = []      # إطارات عرض المحادثات السابقة
oAIProcess = NULL        # معالج الذكاء الاصطناعي
oExecutionProcess = NULL  # معالج تنفيذ الكود

# إعدادات التنسيق
aTextColor = [0,0,0]     # لون النص
aBackgroundColor = [255,255,255]  # لون الخلفية
nDefaultFontSize = 16    # حجم الخط الافتراضي
cFontSettings = "MS Shell Dlg 2," + nDefaultFontSize + ",-1,5,50,0,0,0,0,0"

# إعدادات API وملفات النظام
GEMINI_2_thinking_API_URL = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-thinking-exp-1219:generateContent?key="
GEMINI_2_API_URL = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-exp:generateContent?key="
GEMINI_gemini_exp_1206_API_URL = "https://generativelanguage.googleapis.com/v1beta/models/gemini-exp-1206:generateContent?key="
GEMINI_1_API_URL = "https://generativelanguage.googleapis.com/v1/models/gemini-1.5-flash:generateContent?key="
GEMINI_API_KEY = "AIzaSyAisNXkhbaKM3qhl-v3hxRsSf17wJDAMbU"
cHistoryFilePath = "chat_history.ring"  # مسار ملف حفظ المحادثات


# المتغيرات العامة

oNotebookToolbar = NULL
oCell = NULL
oCellLayout = NULL
aChatMessages = []  # مصفوفة لتخزين رسائل المحادثة
aChatHistory = []   # مصفوفة لتخزين سجل المحادثات
cCurrentFileName = NULL  # اسم الملف الحالي
nDefaultFontSize = 18   # حجم الخط الافتراضي
isDarkMode = false      # متغير للتحكم في النمط (فاتح/داكن)
updatefontsize = false  # متغير للتحكم في تحديث حجم الخط

# قائمة لتخزين الخلايا
aCells = []

# تحميل المحادثات عند بدء البرنامج
loadChatHistory()

# =========== إنشاء التطبيق ===========
MyApp = New qApp {
    win1 = new qMainWindow() {
        setwindowtitle("المساعد المتكامل")
        setGeometry(100,100,1200,800)
        
        # إنشاء شريط الأدوات
        aBtns = [
            # مجموعة الملفات
            new qToolButton(win1) {
                setText("جديد")
                setclickevent("pNew()")
                setStyleSheet("
                    QToolButton { 
                        padding: 4px 8px;
                        margin: 2px;
                        background-color: #27ae60;
                        color: white;
                        border: none;
                        border-radius: 3px;
                        font-weight: bold;
                        min-width: 60px;
                    }
                    QToolButton:hover { 
                        background-color: #2ecc71;
                    }
                    QToolButton:pressed { 
                        background-color: #219a52;
                    }
                ")
            },
            new qToolButton(win1) {
                setText("فتح")
                setclickevent("pOpen()")
                setStyleSheet("
                    QToolButton { 
                        padding: 4px 8px;
                        margin: 2px;
                        background-color: #2980b9;
                        color: white;
                        border: none;
                        border-radius: 3px;
                        font-weight: bold;
                        min-width: 60px;
                    }
                    QToolButton:hover { 
                        background-color: #3498db;
                    }
                    QToolButton:pressed { 
                        background-color: #2471a3;
                    }
                ")
            },
            new qToolButton(win1) {
                setText("حفظ")
                setclickevent("pSave()")
                setStyleSheet("
                    QToolButton { 
                        padding: 4px 8px;
                        margin: 2px;
                        background-color: #f39c12;
                        color: white;
                        border: none;
                        border-radius: 3px;
                        font-weight: bold;
                        min-width: 60px;
                    }
                    QToolButton:hover { 
                        background-color: #f1c40f;
                    }
                    QToolButton:pressed { 
                        background-color: #d68910;
                    }
                ")
            },
            new qToolButton(win1) {
                setText("إغلاق")
                setclickevent("pClose()")
                setStyleSheet("
                    QToolButton { 
                        padding: 4px 8px;
                        margin: 2px;
                        background-color: #c0392b;
                        color: white;
                        border: none;
                        border-radius: 3px;
                        font-weight: bold;
                        min-width: 60px;
                    }
                    QToolButton:hover { 
                        background-color: #e74c3c;
                    }
                    QToolButton:pressed { 
                        background-color: #a93226;
                    }
                ")
            },
            new qToolButton(win1) {
                setText("إرسال للمساعد")
                setclickevent("sendToAssistant()")
                setStyleSheet("
                    QToolButton { 
                        padding: 4px 8px;
                        margin: 2px;
                        background-color: #8e44ad;
                        color: white;
                        border: none;
                        border-radius: 3px;
                        font-weight: bold;
                        min-width: 60px;
                    }
                    QToolButton:hover { 
                        background-color: #9b59b6;
                    }
                    QToolButton:pressed { 
                        background-color: #7d3c98;
                    }
                ")
            },
            new qToolButton(win1) {
                setText("تشغيل")
                setclickevent("pRun( cCurrentFileName )")
                setStyleSheet("
                    QToolButton { 
                        padding: 4px 8px;
                        margin: 2px;
                        background-color: #27ae60;
                        color: white;
                        border: none;
                        border-radius: 3px;
                        font-weight: bold;
                        min-width: 60px;
                    }
                    QToolButton:hover { 
                        background-color: #2ecc71;
                    }
                    QToolButton:pressed { 
                        background-color: #219a52;
                    }
                ")
            },
            new qToolButton(win1) {
                setText("تحليل النص")
                setclickevent("analyzeAssistantText()")
                setStyleSheet("
                    QToolButton { 
                        padding: 4px 8px;
                        margin: 2px;
                        background-color: #8e44ad;
                        color: white;
                        border: none;
                        border-radius: 3px;
                        font-weight: bold;
                        min-width: 60px;
                    }
                    QToolButton:hover { 
                        background-color: #9b59b6;
                    }
                    QToolButton:pressed { 
                        background-color: #7d3c98;
                    }
                ")
            },
            new qToolButton(win1) {
                setText("")  # رمز القمر/الشمس
                setclickevent("toggleTheme()")
                setStyleSheet("
                    QToolButton { 
                        padding: 4px 8px;
                        margin: 2px;
                        background-color: #2c3e50;
                        color: white;
                        border: none;
                        border-radius: 3px;
                        font-weight: bold;
                        min-width: 60px;
                    }
                    QToolButton:hover { 
                        background-color: #34495e;
                    }
                    QToolButton:pressed { 
                        background-color: #2c3e50;
                    }
                ")
            }
        ]

        tool1 = addtoolbar("tools") {
            nCount = 0
            for x in aBtns {
                addwidget(x)
                nCount++
                if nCount = 3 or nCount = 6  # إضافة فاصل بعد مجموعة الملفات والتحرير
                    addseparator()
                ok
            }
        }

        # إنشاء القوائم
        menu1 = new qmenubar(win1) {
            sub1 = addmenu("ملف")
            sub2 = addmenu("تحرير")
            sub3 = addmenu("عرض")
            sub4 = addmenu("مساعدة")
            
            sub1 {
                oAction = new qAction(win1) {
                    setText("جديد")
                    setclickevent("pNew()")
                }
                addaction(oAction)
                oAction = new qAction(win1) {
                    setText("فتح")
                    setclickevent("pOpen()")
                }
                addaction(oAction)
                oAction = new qAction(win1) {
                    setText("حفظ")
                    setclickevent("pSave()")
                }
                addaction(oAction)
                oAction = new qAction(win1) {
                    setText("إغلاق")
                    setclickevent("pClose()")
                }
                addaction(oAction)
                addseparator()
                oAction = new qAction(win1) {
                    setText("إرسال للمساعد")
                    setclickevent("sendToAssistant()")
                }
                addaction(oAction)
            }
            
            sub3 {
                oAction = new qAction(win1) {
                    setText("تكبير الخط")
                    setclickevent("increaseFontSize()")
                }
                addaction(oAction)
                
                oAction = new qAction(win1) {
                    setText("تصغير الخط")
                    setclickevent("decreaseFontSize()")
                }
                addaction(oAction)
                
                addseparator()
                
                oAction = new qAction(win1) {
                    setText("حجم الخط الافتراضي")
                    setclickevent("resetFontSize()")
                }
                addaction(oAction)
            }
        }
        setmenubar(menu1)

        # إنشاء شريط الحالة
        status1 = new qstatusbar(win1) {
            showmessage("جاهز!",0)
        }
        setstatusbar(status1)

        # منطقة عرض الملفات
        tree1 = new qtreeview(win1) {
            setclickedevent("pChangeFile()")
            setStyleSheet("
                QTreeView {
                    font-size: " + nDefaultFontSize + "px;
                    padding: 5px;
                }
                QTreeView::item {
                    padding: 5px;
                }
            ")
            oDir = new QDir()
            ofile = new QFileSystemModel() {
                setrootpath(oDir.currentpath())
                myfiles = new qstringlist()
                myfiles.append("*.ring")
                myfiles.append("*.json")
                myfiles.append("*.rh")
                setnamefilters(myfiles)
                setNameFilterDisables(false)
            }
            setmodel(ofile)
            myindex = ofile.index(oDir.currentpath(),0)
            for x = 1 to ofile.columncount()
                hidecolumn(x)
            next
            setcurrentindex(myindex)
            setexpanded(myindex,true)
            header().hide()
        }

        # إنشاء Dock Widget للملفات
        oDock1 = new qdockwidget(win1,0) {
            setwindowtitle("الملفات")
            setwidget(tree1)
            setFeatures(0x01 | 0x02 | 0x04)  # Movable | Floatable | Closable
            setminimumwidth(200)  # عرض ثابت للملفات
        }

        # إنشاء Dock Widget للمساعد
        oDock = new qdockwidget(win1,0) {
            setWindowTitle("المساعد")
            setFeatures(0x01 | 0x02 | 0x04)  # Movable | Floatable | Closable
            //setAllowedAreas(Qt_RightDockWidgetArea)  # السماح فقط بالجانب الأيمن
            setminimumwidth(250)  # عرض ثابت للمساعد
            
            oWidget = new qWidget() {
                oLayout = new qVBoxLayout() {
                    setContentsMargins(0,0,0,0)
                    
                    # أزرار التحكم في الأعلى
                    oButtonWidget = new qWidget() {
                        oButtonLayout = new qHBoxLayout() {
                            setContentsMargins(10,5,10,10)
                            setSpacing(10)
                            
                            btnNew = new qPushButton(oButtonWidget) {
                                setText("+")
                                setStyleSheet("
                                    QPushButton { 
                                        background-color: #2980b9;
                                        color: white;
                                        border: none;
                                        border-radius: 4px;
                                        padding: 8px 16px;
                                        font-weight: bold;
                                    }
                                    QPushButton:hover { 
                                        background-color: #3498db;
                                    }
                                ")
                                setFixedHeight(30)  # تحديد ارتفاع ثابت للزر
                                setclickevent("newConversation()")
                            }
                            addWidget(btnNew)
                            
                            btnHistory = new qPushButton(oButtonWidget) {
                                setText("السجل")
                                setStyleSheet("
                                    QPushButton { 
                                        background-color: #27ae60;
                                        color: white;
                                        border: none;
                                        border-radius: 4px;
                                        padding: 8px 16px;
                                        font-weight: bold;
                                    }
                                    QPushButton:hover { 
                                        background-color: #2ecc71;
                                    }
                                ")
                                setFixedHeight(30)  # تحديد ارتفاع ثابت للزر
                                setclickevent("showConversationsHistory()")
                            }
                            addWidget(btnHistory)
                            
                            btnClose = new qPushButton(oButtonWidget) {
                                setText("إغلاق")
                                setStyleSheet("
                                    QPushButton { 
                                        background-color: #c0392b;
                                        color: white;
                                        border: none;
                                        border-radius: 4px;
                                        padding: 8px 16px;
                                        font-weight: bold;
                                    }
                                    QPushButton:hover { 
                                        background-color: #e74c3c;
                                    }
                                ")
                                setFixedHeight(30)  # تحديد ارتفاع ثابت للزر
                                setclickevent("closeConversation()")
                                hide()
                            }
                            addWidget(btnClose)
                        }
                        setLayout(oButtonLayout)
                    }
                    addWidget(oButtonWidget)
                    
                    # مربع عرض المحادثات السابقة
                    historyTextEdit = new qTextEdit(oWidget) {
                        setReadOnly(true)
                        hide()
                    }
                    addWidget(historyTextEdit)
                    
                    # مربع عرض المحادثة الحالية
                    oChatEditor = new qTextEdit(oWidget) {
                        setReadOnly(true)
                    }
                    addWidget(oChatEditor)
                    
                    # مربع الإدخال في الأسفل
                    oInputWidget = new qWidget() {
                        setContentsMargins(0,0,0,0)
                        setFixedHeight(35)  # تحديد ارتفاع ثابت لمنطقة الإدخال كاملة
                        oInputLayout = new qHBoxLayout() {
                            setContentsMargins(5,2,5,5)  # تقليل الهوامش
                            setSpacing(5)  # تقليل المسافة بين العناصر
                            
                            oInput = new qLineEdit(oInputWidget) {
                                setStyleSheet("
                                    QLineEdit {
                                        border: 1px solid #bdc3c7;
                                        border-radius: 3px;
                                        padding: 4px;
                                    }
                                ")
                                setFixedHeight(30)  # تقليل الارتفاع
                                
                                # معالجة حدث الضغط على المفاتيح
                                setReturnPressedEvent("sendMessage()")
                            }
                            addWidget(oInput)
                            
                            oSendBtn = new qPushButton(oInputWidget) {
                                setText("إرسال")
                                setStyleSheet("
                                    QPushButton {
                                        padding: 4px 8px;
                                        background-color: #2980b9;
                                        color: white;
                                        border: none;
                                        border-radius: 3px;
                                        font-weight: bold;
                                        min-width: 50px;
                                    }
                                    QPushButton:hover {
                                        background-color: #3498db;
                                    }
                                    QPushButton:pressed {
                                        background-color: #2475a8;
                                    }
                                ")
                                setFixedHeight(30)  # تحديد ارتفاع ثابت للزر
                                setclickevent("sendMessage()")
                            }
                            addWidget(oSendBtn)
                        }
                        setLayout(oInputLayout)
                        hide()
                    }
                    addWidget(oInputWidget)
                }
                setLayout(oLayout)
            }
            setWidget(oWidget)
        }
        addDockWidget(Qt_RightDockWidgetArea, oDock,1)

        # إنشاء Dock Widget لمحرر الكود
        oDock3 = new qdockwidget(win1,0) {
            setwindowtitle("محرر الكود")
            setFeatures(0x01 | 0x02 | 0x04)  # Movable | Floatable | Closable
            setminimumwidth(700)  # عرض ثابت للمحرر 
            oWidget3 = new qWidget() {
                oLayout3 = new qVBoxLayout() {
                    setContentsMargins(0,0,0,0)
                    
                    # شريط أدوات التبديل
                    oSwitchToolbar = new qWidget() {
                        setContentsMargins(0,0,0,0)
                        oSwitchLayout = new qHBoxLayout() {
                            setContentsMargins(5,2,5,2)
                            setSpacing(5)

                            oToggleBtn = new qPushButton(oSwitchToolbar) {
                                setText("تبديل المحرر")
                                setIcon(new qIcon(new qPixmap("icons/toggle.png")))
                                setStyleSheet("
                                    QPushButton {
                                        padding: 4px 12px;
                                        background-color: #2980b9;
                                        color: white;
                                        border: none;
                                        border-radius: 3px;
                                    }
                                    QPushButton:hover {
                                        background-color: #3498db;
                                    }
                                ")
                                setFixedHeight(30)
                                setclickevent("toggleView()")
                            }
                            addWidget(oToggleBtn)
                            addStretch(1)
                        }
                        setLayout(oSwitchLayout)
                    }
                    addWidget(oSwitchToolbar)

                    # حاوية العناصر القابلة للتبديل
                    oStack = new qStackedWidget(oWidget3) {
                        # المحرر العادي
                        oCodeEditor = new CodeEditor(win1) {
                            setStyleSheet("
                               CodeEditor { 
                                    background-color: #1e1e1e;
                                    color: #d4d4d4;
                                    font-family: 'Consolas';
                                    font-size: " + nDefaultFontSize + "px;
                                    padding: 10px;
                                    border: none;
                                }
                                CodeEditor QWidget#LineNumberArea {
                                    font-family: 'Consolas';
                                    font-size: 10px;
                                    padding-top: 4px;
                                }
                           ")
                            
                            SetActiveLineColor( new qColor() { setrgb(128,128,128,255) })
                            setLineNumbersAreaColor( new qColor() { setrgb(255,255,255,255) })
                            setLineNumbersAreaBackColor(new qColor() { setrgb(0,0,0,255) })
                            setLineWrapMode(QTextEdit_NoWrap)
                            new RingCodeHighLighter( oCodeEditor.document() ) {
                            setColors(
                                        new qcolor() { setrgb(30,220,175,255) },
                                        new qcolor() { setrgb(166,226,46,255) },
                                        new qcolor() { setrgb(117,160,172,157)},
                                        new qcolor() { setrgb(230,191,77,255) },
                                        new qcolor() { setrgb(240,127,224,255)}
                                        )
                            }
                        }
                        addWidget(oCodeEditor)

                        # محرر الخلايا
                        oNotebookWidget = new qWidget() {
                            oNotebookLayout = new qVBoxLayout() {
                                setContentsMargins(0,0,0,0)
                                setSpacing(0)

                                # شريط أدوات الخلايا
                                oNotebookToolbar = new qWidget() {
                                    setFixedHeight(40)
                                    setStyleSheet("
                                        QWidget {
                                            background-color: #f8f9fa;
                                            border-bottom: 1px solid #ddd;
                                        }
                                        QPushButton {
                                            padding: 5px 10px;
                                            color: white;
                                            border: none;
                                            border-radius: 3px;
                                            margin-right: 5px;
                                        }
                                        QPushButton:hover {
                                            opacity: 0.8;
                                        }
                                    ")
                                    
                                    oToolbarLayout = new qHBoxLayout() {
                                        setContentsMargins(10,5,10,5)
                                        setSpacing(5)

                                        # زر إضافة خلية
                                        oAddCellBtn = new qPushButton(oNotebookToolbar) {
                                            setText("+ إضافة خلية")
                                            setStyleSheet("
                                                QPushButton {
                                                    background-color: #2ecc71;
                                                }
                                                QPushButton:hover {
                                                    background-color: #27ae60;
                                                }
                                            ")
                                            setclickevent("addNewCell()")
                                        }
                                        addWidget(oAddCellBtn)

                                        # زر تشغيل جميع الخلايا
                                        oRunAllBtn = new qPushButton(oNotebookToolbar) {
                                            setText("▶ تشغيل الكل")
                                            setStyleSheet("
                                                QPushButton {
                                                    background-color: #3498db;
                                                }
                                                QPushButton:hover {
                                                    background-color: #2980b9;
                                                }
                                            ")
                                            setclickevent("runAllCells()")
                                        }
                                        addWidget(oRunAllBtn)

                                        # زر حفظ النوتبوك
                                        oSaveBtn = new qPushButton(oNotebookToolbar) {
                                            setText("💾 حفظ")
                                            setStyleSheet("
                                                QPushButton {
                                                    background-color: #e67e22;
                                                }
                                                QPushButton:hover {
                                                    background-color: #d35400;
                                                }
                                            ")
                                            setclickevent("saveNotebook()")
                                        }
                                        addWidget(oSaveBtn)

                                        # زر فتح نوتبوك
                                        oOpenBtn = new qPushButton(oNotebookToolbar) {
                                            setText("📂 فتح")
                                            setStyleSheet("
                                                QPushButton {
                                                    background-color: #9b59b6;
                                                }
                                                QPushButton:hover {
                                                    background-color: #8e44ad;
                                                }
                                            ")
                                            setclickevent("openNotebook()")
                                        }
                                        addWidget(oOpenBtn)

                                        addStretch(1)
                                    }
                                    setLayout(oToolbarLayout)
                                }
                                addWidget(oNotebookToolbar)

                                # منطقة التمرير للخلايا
                                oScrollArea = new qScrollArea(oNotebookWidget) {
                                    setWidgetResizable(true)
                                    setStyleSheet("
                                        QScrollArea {
                                            border: none;
                                        }
                                    ")
                                    
                                    oNotebookEditor = new qWidget() {
                                        setObjectName("notebookEditor")
                                        oNotebookEditorLayout = new qVBoxLayout() {
                                            setContentsMargins(10,10,10,10)
                                            setSpacing(0)
                                        }
                                        setLayout(oNotebookEditorLayout)
                                    }
                                    setWidget(oNotebookEditor)
                                }
                                addWidget(oScrollArea)
                            }
                            setLayout(oNotebookLayout)
                        }
                        addWidget(oNotebookWidget)
                    }
                    addWidget(oStack)
                    
                    
                }
                setLayout(oLayout3)
            }
            setWidget(oWidget3)
        }
        addDockWidget(Qt_RightDockWidgetArea, oDock3,1)

        # إنشاء نافذة الناتج
        textedit_output = new qtextedit(win1) {
            setStyleSheet("
                background-color: #1e1e1e;
                color: #ffffff;
                font-family: 'Consolas';
                font-size: " + nDefaultFontSize + "px;
                padding: 10px;
            ")
            setReadOnly(true)
        }
        
        oDock4 = new qdockwidget(win1,0) {
            setwindowtitle("الناتج")
            setwidget(textedit_output)
            setFeatures(0x01 | 0x02)  # Movable | Floatable only
            setminimumheight(100)  # ارتفاع أدنى للناتج
            hide()  # إخفاء النافذة عند البداية
        }

        # إضافة Dock Widgets إلى النافذة
        win1 {
            # إضافة النوافذ الجانبية
            addDockWidget(1, oDock1, 1)  # الملفات على اليسار
            
            # إضافة المحرر في المنطقة المركزية
            addDockWidget(1, oDock3, 1)  # Qt::DockWidgetArea = 4
            
            # إضافة نافذة الناتج في الأسفل
            addDockWidget(8, oDock4, 1)  # Qt::BottomDockWidgetArea = 8
        }

        showmaximized()
    }
    exec()
}

# عرض المحادثات السابقة
displayChatHistory()

# إخفاء عناصر المساعد عند بدء البرنامج
oSendBtn.hide()
btnClose.hide()
oInputWidget.hide()

# =========== دالات المساعد ===========

/*
الدالة: saveChatHistory
الوصف: حفظ المحادثات في ملف
المدخلات: لا يوجد
المخرجات: true إذا نجحت العملية، false إذا فشلت
*/
func saveChatHistory
    try {
        # إضافة المحادثة الحالية إلى السجل
        if len(aChatMessages) > 0
            add(aChatHistory, aChatMessages)
            aChatMessages = []  # تفريغ المحادثة الحالية
        ok
        CodeCatHistory = "aChatHistory = " + list2Code(aChatHistory)  # تحويل المحادثات لنص JSON
        write(cHistoryFilePath, CodeCatHistory)  # حفظ المحادثات في الملف    
    catch
        ? "خطأ في حفظ المحادثات: " + cCatchError + nl
    }
    return false

/*
الدالة: loadChatHistory
الوصف: تحميل سجل المحادثات من الملف
المدخلات: لا يوجد
المخرجات: لا يوجد
*/
func loadChatHistory
   try {
        if not fexists(cHistoryFilePath)
            aChatHistory = []  # تهيئة السجل إذا لم يكن الملف موجوداً
            return
        ok
       cContent = read(cHistoryFilePath)
       //? cContent
        eval(cContent)  # تحميل المحادثات في aChatHistory
    catch
        ? "خطأ في تحميل المحادثات: " + cCatchError + nl
    }

/*
الدالة: displayChatHistory
الوصف: عرض سجل المحادثات في الواجهة
المدخلات: لا يوجد
المخرجات: لا يوجد
*/
func displayChatHistory
    oChatEditor.hide()
    oInputWidget.hide()
    btnClose.hide()
    historyTextEdit.show()
    loadChatHistory()
    # إزالة الإطارات القديمة إن وجدت
    for frame in aHistoryFrames
        frame.hide()
        frame.delete()
    next
    aHistoryFrames = []
    
    for i = 1 to len(aChatHistory)
        cPreview = ""
        if len(aChatHistory[i]) > 0
            # البحث عن أول رسالة من المستخدم
            for message in aChatHistory[i]
                if message[:role] = "user"
                    cPreview = message[:text]
                    if len(cPreview) > 40
                        cPreview = substr(cPreview, 1, 40) + "..."
                    ok
                    exit
                ok
            next
        ok
        
        # إنشاء زر للمحادثة
        frameBtn = new qPushButton(historyTextEdit) {
            setGeometry(10, 10 + 60 * (i-1), historyTextEdit.width() - 20, 50)
            setStyleSheet("
                QPushButton { 
                    background-color: #ffffff;
                    border: 1px solid #ddd;
                    border-radius: 4px;
                    text-align: right;
                    padding: 5px;
                }
                QPushButton:hover { 
                    background-color: #f5f5f5;
                }
            ")
            
            # إضافة نص الزر
            cButtonText = " محادثة " + i
            if len(cPreview) > 0
                cButtonText += nl + cPreview
            ok
            setText(cButtonText)
            
            setclickevent("loadConversation(" + i + ")")
            
            # إضافة زر الحذف
            deleteBtn = new qpushbutton(frameBtn) {
                setText("x")
                setgeometry(frameBtn.width() - 350, 1, 30, 30)
                setstylesheet("
                    QPushButton {
                        background-color: transparent;
                        border: none;
                        color: #666;
                        font-size: 16pt;
                    }
                    QPushButton:hover {
                        color: #ff0000;
                    }
                ")
                setclickevent("deleteConversation(" + i + ")")
            }
            
            show()
        }
        add(aHistoryFrames, frameBtn)
    next

/*
الدالة: deleteConversation
الوصف: حذف محادثة محددة من السجل
المدخلات: 
    - nIndex: رقم المحادثة المراد حذفها
المخرجات: لا يوجد
*/
func deleteConversation nIndex
    see "deleteConversation index: " + nIndex + nl
    see "aChatHistory length before: " + len(aChatHistory) + nl
    if nIndex >= 1 and nIndex <= len(aChatHistory)
        del(aChatHistory, nIndex)
        see "aChatHistory length after: " + len(aChatHistory) + nl
        saveChatHistory()
        displayChatHistory()
        status1.showmessage("تم حذف المحادثة",0)
    ok

/*
الدالة: loadConversation
الوصف: تحميل محادثة محددة من السجل
المدخلات:
    - nIndex: رقم المحادثة المراد تحميلها
المخرجات: لا يوجد
*/
func loadConversation nIndex
    if nIndex >= 1 and nIndex <= len(aChatHistory)
        aChatMessages = aChatHistory[nIndex]
        historyTextEdit.hide()
        oChatEditor.show()
        oChatEditor.setPlainText("")
        updateChatDisplay()
        oInputWidget.show()
        btnClose.show()
        status1.showmessage("تم تحميل المحادثة " + nIndex,0)
    ok

/*
الدالة: showConversationsHistory
الوصف: عرض نافذة سجل المحادثات
المدخلات: لا يوجد
المخرجات: لا يوجد
*/
func showConversationsHistory
    if len(aChatHistory) = 0
        status1.showmessage("لا توجد محادثات سابقة",0)
        return
    ok
    displayChatHistory()
    status1.showmessage("تم عرض المحادثات السابقة",0)

/*
الدالة: loadSelectedConversation
الوصف: تحميل المحادثة المحددة من القائمة
المدخلات: لا يوجد
المخرجات: لا يوجد
*/
func loadSelectedConversation
    nIndex = oFileList.currentRow() + 1
    if nIndex >= 1 and nIndex <= len(aChatHistory)
        aChatMessages = aChatHistory[nIndex]
        oChatEditor.show()
        oInputWidget.show()
        btnClose.show()
        updateChatDisplay()
        status1.showmessage("تم تحميل المحادثة " + nIndex,0)
    ok

/*
الدالة: updateChatDisplay
الوصف: تحديث عرض المحادثة الحالية في الواجهة
المدخلات: لا يوجد
المخرجات: لا يوجد
*/
func updateChatDisplay
    oChatEditor.setPlainText("")
    
    for message in aChatMessages
        if message[:role] = "user"
            oChatEditor.append(" أنت:" + nl)
            oChatEditor.append(message[:text] + nl + nl)
        else
            oChatEditor.append(" المساعد:" + nl)
            # تحليل الرد واستخراج الكود
            oExtractor = new CodeExtractor
            oExtractor.cText = message[:text]
            cHighlighted = oExtractor.highlightCode(message[:text])
            oChatEditor.append(cHighlighted + nl + nl)
            # إضافة أزرار للكود
            oExtractor.addCodeButtons(oChatEditor)
        ok
    next
    
    # تحريك المؤشر إلى نهاية النص
    oCursor = oChatEditor.textCursor()
    nPos = max(len(oChatEditor.toPlainText())-1,0)
    oCursor.setPosition(nPos,QTextCursor_KeepAnchor)
    oCursor.setPosition(nPos,QTextCursor_MoveAnchor)
    oChatEditor.setTextCursor(oCursor)

/*
الدالة: saveConversation
الوصف: حفظ المحادثة الحالية في السجل
المدخلات: لا يوجد
المخرجات: لا يوجد
*/
func saveConversation
    try {
        # إنشاء نسخة من المحادثة الحالية
        aCurrentChat = []
        for message in aChatMessages
            add(aCurrentChat, message)
        next
        
        # إضافة المحادثة إلى التاريخ
        add(aChatHistory, aCurrentChat)
        
        # حفظ التاريخ في الملف
        write(cHistoryFilePath, list2str(aChatHistory))
        
        status1.showmessage("تم حفظ المحادثة", 0)
    catch
        status1.showmessage("حدث خطأ في حفظ المحادثة: " + cCatchError, 0)
    }

/*
الدالة: newConversation
الوصف: بدء محادثة جديدة
المدخلات: لا يوجد
المخرجات: لا يوجد
*/
func newConversation
    aChatMessages = []
    oChatEditor.setPlainText("")
    oChatEditor.show()
    oInputWidget.show()
    status1.showmessage("تم إنشاء محادثة جديدة",0)
    btnClose.show()
    historyTextEdit.hide()

/*
الدالة: closeConversation
الوصف: إغلاق المحادثة الحالية مع إمكانية الحفظ
المدخلات: لا يوجد
المخرجات: لا يوجد
*/
func closeConversation
    if len(aChatMessages) > 0
        oMsg = new qMessageBox(win1) {
            setwindowtitle("تنبيه")
            settext("هل تريد حفظ المحادثة قبل إغلاقها؟")
            setstandardbuttons(QMessageBox_Yes | QMessageBox_No | QMessageBox_Cancel)
            result = exec()
            switch result {
                case QMessageBox_Yes
                    saveConversation()
                    aChatMessages = []
                case QMessageBox_No
                    aChatMessages = []
                case QMessageBox_Cancel
                    return
            }
        }
    ok
    oChatEditor.setPlainText("")
    oInputWidget.hide()
    btnClose.hide()
    status1.showmessage("تم إغلاق المحادثة",0)
    displayChatHistory()

/*
الدالة: sendMessage
الوصف: إرسال رسالة إلى المساعد
المدخلات: لا يوجد
المخرجات: لا يوجد
*/
func sendMessage
    try {
        cMessage = oInput.Text()
       
        if cMessage = NULL or cMessage = ""
            raise("الرسالة فارغة")
        ok
        
        # إذا كانت أول رسالة، نضيف القواعد
        if len(aChatMessages) = 0
            # قراءة القواعد من الملف
            cRules = read("rules_new.txt")
            cMessage = cRules + nl + nl + cMessage
        ok
        
        # إضافة الرسالة
        add(aChatMessages, [:role = "user", :text = cMessage])
        
        updateChatDisplay()
        oChatEditor.append("جاري المعالجة..." + nl)
        
        oProcess = runprocess(cMessage)
        if oProcess = NULL
            raise("فشل في بدء المعالجة")
        ok
        
        oInput.setText("")
    catch
        status1.showmessage("خطأ في إرسال الرسالة: " + cCatchError, 0)
    }

/*
الدالة: runprocess
الوصف: تشغيل عملية معالجة الرسالة باستخدام الذكاء الاصطناعي
المدخلات:
    - cMessage: نص الرسالة المراد معالجتها
المخرجات: عملية المعالجة
*/
func runprocess cMessage

    oAIProcess = new QProcess(NULL) {
        oArgList = new QStringList() {
            append("-s")
            append("-X")
            append("POST")
            append("-H")
            append("Content-Type: application/json")
            append(GEMINI_2_API_URL + GEMINI_API_KEY)
            append("-d")
            append(buildConversationJson())
        }
        setprogram("curl")
        setArguments(oArgList)
        setreadyreadstandardoutputevent("processOutput()")
        setreadyReadStandardErrorEvent("PGetError()")
        start_3(QIODevice_ReadWrite)
        waitForFinished(100)
    }
    return oAIProcess

/*
الدالة: PGetError
الوصف: معالجة الأخطاء التي تحدث أثناء معالجة الرسالة
المدخلات: لا يوجد
المخرجات: لا يوجد
*/
func PGetError
    if ISNULL(oAIProcess) return ok
    cError = oAIProcess.readallstandardError()
    oChatEditor.append(" حدث خطأ: " + nl + cError.data())

/*
الدالة: processOutput
الوصف: معالجة مخرجات المساعد الذكي
المدخلات: لا يوجد
المخرجات: لا يوجد
*/
func processOutput
    if ISNULL(oAIProcess) return ok
    
   // try {
        cOutput = oAIProcess.readallstandardoutput()
        
        # التحقق من أن المخرجات ليست فارغة
        if cOutput = NULL or cOutput = "" 
            status1.showmessage("لا توجد مخرجات", 0)
            return 
        ok
        ? cOutput.data()
        # تنظيف النص وتحليله
        cCleanJsonText = cleanTextFromAssistant(cOutput.data())
        ? cCleanJsonText
        //oExtractor = new CodeExtractor
        //analyzeText = oExtractor.cleanHtmlTags(cCleanJsonText)
        assistantCleanText = Json2list(cCleanJsonText)
        cleanText = assistantCleanText["candidates"][1]["content"]["parts"][1]["text"]
        # إضافة النص مباشرة إلى نافذة المحادثة
        oChatEditor.append(" المساعد:" + nl)
        oChatEditor.append(cleanText + nl + nl)
        
        # إضافة الرد إلى المحادثات
        add(aChatMessages, [:role = "assistant", :text = cleanText])
        
        # استخراج الكود
        analyzeAssistantText()
        # تفعيل حقل الإدخال
        oInput.setEnabled(true)
        
        # تمرير التركيز إلى حقل الإدخال
        oInput.setFocus(true)
        
   /* catch
        status1.showmessage("حدث خطأ في معالجة الاستجابة: " + cCatchError, 0)
    }*/

/*
الدالة: analyzeAssistantText
الوصف: تحليل النص الذي تم إرساله من قبل المساعد
المدخلات: لا يوجد
المخرجات: لا يوجد
*/
     
func analyzeAssistantText
    # الحصول على النص من محرر المحادثة
    cText = oChatEditor.toPlainText()
    
    # إنشاء كائن CodeExtractor
    oExtractor = new CodeExtractor(cText)
    
    # استخراج الكود
    cExtractedCode = oExtractor.getExtractedCode()
    
    # عرض الكود في المحرر
    if cExtractedCode != "لم يتم العثور على كود للاستخراج"
        oCodeEditor.setPlainText(cExtractedCode)
        # التبديل إلى المحرر العادي
        oStack.setCurrentIndex(0)
        status1.showMessage("تم استخراج الكود بنجاح", 3000)
    else
        status1.showMessage("لم يتم العثور على كود للاستخراج", 3000)
    ok

/*
الدالة: buildConversationJson
الوصف: تحويل المحادثة إلى تنسيق JSON
المدخلات: لا يوجد
المخرجات: نص JSON يمثل المحادثة
*/
func buildConversationJson
    cJson = "{"
    cJson += '"contents": ['
    for i = 1 to len(aChatMessages)
        if i > 1 cJson += "," ok
        cJson += '{"role": "' + aChatMessages[i][:role] + '",'
        cJson += '"parts": [{"text": "' + prepareTextToAssistant(aChatMessages[i][:text]) + '"}]}'
    next
    cJson += "]}"
    return cJson

# =========== دالات المحرر ===========
/*
الدالة: pChangeFile
الوصف: تغيير الملف الحالي مع التحقق من حفظ التغييرات
المدخلات: لا يوجد
المخرجات: لا يوجد
*/
func pChangeFile
    try 
        cCurrentFileName = ofile.filepath(tree1.currentindex())
        oCodeEditor.setPlainText(read(cCurrentFileName))
        status1.showmessage("تم فتح الملف: " + cCurrentFileName, 0)
    catch
        status1.showmessage("خطأ: " + cCatchError, 0)
    done

/*
الدالة: pNew
الوصف: إنشاء ملف جديد
المدخلات: لا يوجد
المخرجات: لا يوجد
*/
func pNew
    new qfiledialog(win1) {
        cName = getsavefilename(win1,"حفظ الملف الجديد","","Ring Files(*.ring)")
        if cName != NULL
            cCurrentFileName = cName
            oCodeEditor.setPlainText("")
            write(cCurrentFileName, "")
            status1.showmessage("تم إنشاء ملف جديد: " + cCurrentFileName, 0)
        ok
    }

/*
الدالة: pOpen
الوصف: فتح ملف موجود
المدخلات: لا يوجد
المخرجات: لا يوجد
*/
func pOpen
    new qfiledialog(win1) {
        cName = getopenfilename(win1,"فتح ملف","","source files(*.ring)")
        if cName != NULL
            cCurrentFileName = cName
            oCodeEditor.setPlainText(read(cCurrentFileName))
            
        ok
    }

/*
الدالة: pSave
الوصف: حفظ محتوى الملف الحالي
المدخلات: لا يوجد
المخرجات: لا يوجد
*/
func pSave
    try {
        if cCurrentFileName = NULL
            new qfiledialog(win1) {
                cName = getsavefilename(win1,"حفظ باسم","","source files(*.ring)")
                if cName != NULL
                    cCurrentFileName = cName
                else
                    raise("لم يتم تحديد اسم الملف")
                ok
            }
        ok
        
        if cCurrentFileName != NULL
            cContent = oCodeEditor.toPlainText()
            if write(cCurrentFileName, cContent) = 0
                cError = "خطأ في حفظ الملف: " + cCurrentFileName
                raise(cError)
            ok
            status1.showmessage("تم حفظ الملف: " + cCurrentFileName, 0)
        ok
    catch
        status1.showmessage("خطأ: " + cCatchError, 0)
    }

/*
الدالة: pClose
الوصف: إغلاق الملف الحالي
المدخلات: لا يوجد
المخرجات: لا يوجد
*/
func pClose
    if cCurrentFileName != NULL
        oMsg = new qMessageBox(win1) {
            setwindowtitle("تنبيه")
            settext("هل تريد حفظ التغييرات قبل الإغلاق؟")
            setstandardbuttons(QMessageBox_Yes | QMessageBox_No | QMessageBox_Cancel)
            result = exec()
            switch result {
                case QMessageBox_Yes
                    pSave()
                    if cCurrentFileName != NULL  # تم الحفظ بنجاح
                        oCodeEditor.setPlainText("")
                        cCurrentFileName = NULL
                    ok
                case QMessageBox_No
                    oCodeEditor.setPlainText("")
                    cCurrentFileName = NULL
                case QMessageBox_Cancel
                    return
            }
        }
    ok
    status1.showmessage("تم إغلاق الملف",0)

/*
الدالة: pRun
الوصف: تشغيل الملف الحالي
المدخلات:
    - cFile: اسم الملف المراد تشغيله
المخرجات: لا يوجد
*/
func pRun cFile
    if cFile = NULL
        status1.showmessage("يرجى حفظ الملف أولاً!",0)
        return
    ok
    
    # إظهار نافذة الناتج
    oDock4.show()
    textedit_output.clear()  # مسح الناتج السابق
    
    try {
        oExecutionProcess = new QProcess(NULL) {
            setprogram("ring")
            oArgList = new QStringList() {
                append(cFile)
            }
            setArguments(oArgList)
            setreadyreadstandardoutputevent("processRunOutput()")
            setreadyReadStandardErrorEvent("processRunError()")
            start_3(QIODevice_ReadWrite)
            waitForFinished(5000)  # انتظار 5 ثواني كحد أقصى
        }
        return oExecutionProcess
    catch
        status1.showmessage("حدث خطأ أثناء تشغيل الملف: " + cCatchError, 0)
        return NULL
    }

/*
الدالة: processRunOutput
الوصف: معالجة مخرجات تشغيل الملف
المدخلات: لا يوجد
المخرجات: لا يوجد
*/
func processRunOutput
    if ISNULL(oExecutionProcess) return ok
    cOutput = oExecutionProcess.readallstandardoutput()
    textedit_output.append(cOutput.data())

/*
الدالة: processRunError
الوصف: معالجة الأخطاء التي تحدث أثناء تشغيل الملف
المدخلات: لا يوجد
المخرجات: لا يوجد
*/
func processRunError
    if ISNULL(oExecutionProcess) return ok
    cError = oExecutionProcess.readallstandardError()
    textedit_output.append(" خطأ: " + nl + cError.data())

/*
الدالة: increaseFontSize
الوصف: زيادة حجم الخط
المدخلات: لا يوجد
المخرجات: لا يوجد
*/
func increaseFontSize
    nDefaultFontSize += 2
    updateFontSize()

/*
الدالة: decreaseFontSize
الوصف: تقليل حجم الخط
المدخلات: لا يوجد
المخرجات: لا يوجد
*/
func decreaseFontSize
    if nDefaultFontSize > 8  # لا نسمح بحجم خط أصغر من 8
        nDefaultFontSize -= 2
        updateFontSize()
    ok

/*
الدالة: resetFontSize
الوصف: إعادة ضبط حجم الخط إلى القيمة الافتراضية
المدخلات: لا يوجد
المخرجات: لا يوجد
*/
func resetFontSize
    nDefaultFontSize = 16
    updateFontSize()

/*
الدالة: updateFontSize
الوصف: تحديث حجم الخط في الواجهة
المدخلات: لا يوجد
المخرجات: لا يوجد
*/
func updateFontSize
    if updatefontsize return ok  # منع التكرار
    updatefontsize = true
    # تحديث حجم الخط
    oChatEditor.setFont(new qFont("MS Shell Dlg 2", nDefaultFontSize, 50, 0))
    oCodeEditor.setFont(new qFont("MS Shell Dlg 2", nDefaultFontSize, 50, 0))
    textedit_output.setFont(new qFont("MS Shell Dlg 2", nDefaultFontSize, 50, 0))
    
    # تحديث المظهر حسب النمط الحالي
    if isDarkMode
        # الوضع الليلي
        oChatEditor.setStyleSheet("
            background-color: #1e1e1e;
            color: #d4d4d4;
            font-family: 'MS Shell Dlg 2';
            font-size: " + nDefaultFontSize + "px;
            padding: 10px;
            border: 1px solid #3e3e3e;
            selection-background-color: #264f78;
        ")
        oCodeEditor.setStyleSheet("
            CodeEditor { 
                background-color: #1e1e1e;
                color: #d4d4d4;
                font-family: 'Consolas';
                font-size: " + nDefaultFontSize + "px;
                padding: 10px;
                border: none;
            }
            CodeEditor QWidget#LineNumberArea {
                font-family: 'Consolas';
                font-size: 10px;
                padding-top: 4px;
            }
        ")
        oCodeEditor.setLineNumbersAreaColor( new qColor() { setrgb(255,255,255,255) })
        oCodeEditor.setLineNumbersAreaBackColor(new qColor() { setrgb(30,30,30,255) })
        tree1.setStyleSheet("
            QTreeView {
                background-color: #252526;
                color: #d4d4d4;
            }
        ")
       historyTextEdit.setStyleSheet("
            QTextEdit {
                background-color: #252526;
                color: #d4d4d4;
            }
        ")
        textedit_output.setStyleSheet("
            QTextEdit {
                background-color: #252526;
                font-size: " + nDefaultFontSize + "px;
                color: #d4d4d4;
            }
        ")
    else
        # الوضع النهاري
        oChatEditor.setStyleSheet("
            background-color: white;
            color: black;
            font-family: 'MS Shell Dlg 2';
            font-size: " + nDefaultFontSize + "px;
            padding: 10px;
            border: 1px solid #cccccc;
            selection-background-color: #0078d7;
        ")
        oCodeEditor.setStyleSheet("
            CodeEditor { 
                background-color: #1e1e1e;
                color: #d4d4d4;
                font-family: 'Consolas';
                font-size: " + nDefaultFontSize + "px;
                padding: 10px;
                border: none;
            }
            CodeEditor QWidget#LineNumberArea {
                font-family: 'Consolas';
                font-size: 10px;
                padding-top: 4px;
            }
        ")
        oCodeEditor.setLineNumbersAreaColor( new qColor() { setrgb(255,255,255,255) })
        oCodeEditor.setLineNumbersAreaBackColor(new qColor() { setrgb(30,30,30,255) })
        tree1.setStyleSheet("
            QTreeView {
                background-color: white;
                color: black;
            }
        ")
       
        historyTextEdit.setStyleSheet("
            QTextEdit {
                background-color: white;
                color: black;
            }
        ")
        textedit_output.setStyleSheet("
            QTextEdit {
                background-color: white;
                font-size: " + nDefaultFontSize + "px;
                color: black;
            }
        ")
    ok
    
    updatefontsize = false

/*
الدالة: sendToAssistant
الوصف: إرسال نص محدد للمساعد
المدخلات: لا يوجد
المخرجات: لا يوجد
*/
func sendToAssistant
    selectedText = oCodeEditor.textCursor().selectedText()
    if selectedText = ""
        status1.showmessage("الرجاء تحديد نص أولاً",0)
        return
    ok 
    # تنظيف النص المحدد
    selectedText = trim(selectedText)  
    # إضافة النص إلى حقل الإدخال
    oInput.setText(selectedText)
    # إرسال النص للمساعد
   # sendMessage()

/*
الدالة: toggleTheme
الوصف: تبديل بين نمطي الواجهة (نهاري وليلي)
المدخلات: لا يوجد
المخرجات: لا يوجد
*/
func toggleTheme
    isDarkMode = not isDarkMode
    
    if isDarkMode
        # الوضع الليلي
        win1.setStyleSheet("
            QMainWindow {
                background-color: #252526;
            }
            QToolBar {
                background-color: #333333;
                border: none;
            }
            QDockWidget {
                background-color: #252526;
                color: #d4d4d4;
            }
            QDockWidget::title {
                background-color: #333333;
                padding: 6px;
            }
        ")
        
        oChatEditor.setStyleSheet("
            background-color: #1e1e1e;
            color: #d4d4d4;
            font-family: 'MS Shell Dlg 2';
            font-size: " + nDefaultFontSize + "px;
            padding: 10px;
            border: 1px solid #3e3e3e;
            selection-background-color: #264f78;
        ")
        
        tree1.setStyleSheet("
            QTreeView {
                background-color: #252526;
                color: #d4d4d4;
            }
        ")
        
        historyTextEdit.setStyleSheet("
            QTextEdit {
                background-color: #252526;
                color: #d4d4d4;
            }
        ")
        
        textedit_output.setStyleSheet("
            QTextEdit {
                background-color: #1e1e1e;
                font-size: " + nDefaultFontSize + "px;
                color: #d4d4d4;
            }
        ")
       
    else
        # الوضع النهاري
        win1.setStyleSheet("
            QMainWindow {
                background-color: #f0f0f0;
            }
            QToolBar {
                background-color: #f5f5f5;
                border: none;
            }
            QDockWidget {
                background-color: white;
                color: black;
            }
            QDockWidget::title {
                background-color: #f5f5f5;
                padding: 6px;
            }
        ")
        
        oChatEditor.setStyleSheet("
            background-color: white;
            color: black;
            font-family: 'MS Shell Dlg 2';
            font-size: " + nDefaultFontSize + "px;
            padding: 10px;
            border: 1px solid #cccccc;
            selection-background-color: #0078d7;
        ")
        
    
        tree1.setStyleSheet("
            QTreeView {
                background-color: white;
                color: black;
            }
        ")
        
        historyTextEdit.setStyleSheet("
            QTextEdit {
                background-color: white;
                color: black;
            }
        ")
        
        textedit_output.setStyleSheet("
            QTextEdit {
                background-color: white;
                font-size: " + nDefaultFontSize + "px;
                color: black;
            }
        ")
    ok



# دالة للتبديل إلى عرض محرر الخلايا
func switchToNotebook
    oStack.setCurrentIndex(0)

# دالة للتبديل إلى عرض المحرر
func switchToEditor
    oStack.setCurrentIndex(1)

# دالة للتبديل بين العرضين
func toggleView
    if oStack.currentIndex() = 0
        switchToEditor()
    else
        switchToNotebook()
    ok


# دالة إنشاء خلية جديدة
func createNewCell nCellNumber
    oCell = new qWidget() {
        setObjectName("cell_" + nCellNumber)
        setStyleSheet("
            QWidget {
                margin: 10px 0;
                background-color: white;
                border: 1px solid #ddd;
                border-radius: 4px;
            }
        ")
        
        oCellLayout = new qVBoxLayout() {
            setContentsMargins(0,0,0,0)
            setSpacing(0)

            # عنوان الخلية مع رقمها
            oHeaderWidget = new qWidget() {
                setParent(oCell)
                setFixedHeight(40)
                setStyleSheet("
                    QWidget {
                        background-color: #f8f9fa;
                        border-bottom: 1px solid #ddd;
                    }
                ")
                
                oHeaderLayout = new qHBoxLayout() {
                    setContentsMargins(10,0,10,0)
                    
                    # رقم الخلية
                    oCellLabel = new qLabel(oHeaderWidget) {
                        setObjectName("cellLabel")
                        setParent(oHeaderWidget)
                        setText("خلية [" + nCellNumber + "]")
                        setStyleSheet("
                            QLabel {
                                color: #666;
                                font-weight: bold;
                            }
                        ")
                    }
                    addWidget(oCellLabel)
                    
                    addStretch(1)
                    
                    # نوع الخلية (كود/نص)
                    oCellType = new qComboBox(oHeaderWidget) {
                        setObjectName("cellType")
                        setParent(oHeaderWidget)
                        addItem("كود", 0)
                        addItem("نص", 1)
                        setFixedWidth(80)
                        setStyleSheet("
                            QComboBox {
                                border: 1px solid #ddd;
                                border-radius: 3px;
                                padding: 2px 5px;
                            }
                        ")
                        setCurrentIndex(0)
                    }
                    addWidget(oCellType)
                    
                    # أزرار التحكم
                    btnRun = new qPushButton(oHeaderWidget) {
                        setObjectName("btnRun")
                        setParent(oHeaderWidget)
                        setText("▶")
                        setFixedSize(30,30)
                        setStyleSheet("
                            QPushButton {
                                border: none;
                                background: #28a745;
                                color: white;
                                border-radius: 3px;
                            }
                            QPushButton:hover {
                                background-color: #218838;
                            }
                        ")
                        setclickevent("runCell(" + string(nCellNumber) + ")")
                    }
                    addWidget(btnRun)
                    
                    btnUp = new qPushButton(oHeaderWidget) {
                        setObjectName("btnUp")
                        setParent(oHeaderWidget)
                        setText("↑")
                        setFixedSize(30,30)
                        setStyleSheet("
                            QPushButton {
                                border: none;
                                background: #6c757d;
                                color: white;
                                border-radius: 3px;
                            }
                            QPushButton:hover {
                                background-color: #5a6268;
                            }
                        ")
                        setclickevent("moveCellUp(" + string(nCellNumber) + ")")
                    }
                    addWidget(btnUp)
                    
                    btnDown = new qPushButton(oHeaderWidget) {
                        setObjectName("btnDown")
                        setParent(oHeaderWidget)
                        setText("↓")
                        setFixedSize(30,30)
                        setStyleSheet("
                            QPushButton {
                                border: none;
                                background: #6c757d;
                                color: white;
                                border-radius: 3px;
                            }
                            QPushButton:hover {
                                background-color: #5a6268;
                            }
                        ")
                        setclickevent("moveCellDown(" + string(nCellNumber) + ")")
                    }
                    addWidget(btnDown)
                    
                    btnDelete = new qPushButton(oHeaderWidget) {
                        setObjectName("btnDelete")
                        setParent(oHeaderWidget)
                        setText("×")
                        setFixedSize(30,30)
                        setStyleSheet("
                            QPushButton {
                                border: none;
                                background: #dc3545;
                                color: white;
                                border-radius: 3px;
                            }
                            QPushButton:hover {
                                background-color: #c82333;
                            }
                        ")
                        setclickevent("deleteCell(" + string(nCellNumber) + ")")
                    }
                    addWidget(btnDelete)
                }
                setLayout(oHeaderLayout)
            }
            addWidget(oHeaderWidget)
            
            # محرر النص
            oInputCell = new qPlainTextEdit(oCell) {
                setObjectName("inputCell")
                setParent(oCell)
                setStyleSheet("
                    QPlainTextEdit {
                        border: none;
                        padding: 10px;
                        font-family: 'Courier New';
                    }
                ")
                setMinimumHeight(100)
            }
            addWidget(oInputCell)
            
            # مخرجات التنفيذ
            oOutputCell = new qPlainTextEdit(oCell) {
                setObjectName("outputCell")
                setParent(oCell)
                setStyleSheet("
                    QPlainTextEdit {
                        border: none;
                        border-top: 1px solid #ddd;
                        padding: 10px;
                        background-color: #f8f9fa;
                        color: #666;
                        font-family: 'Courier New';
                    }
                ")
                setReadOnly(true)
                setMinimumHeight(50)
                hide()
            }
            addWidget(oOutputCell)
        }
        setLayout(oCellLayout)
    }
    
    # إضافة الخلية إلى القائمة
    add(aCells, [oCell, "", "", nCellNumber, "code"])
    see "Added cell to aCells, new length: " + len(aCells) + nl
    return oCell

# دالة تغيير نوع الخلية
func cellTypeChanged nIndex
    if nIndex >= 1 and nIndex <= len(aCells)
        oCell = aCells[nIndex][1]
        # استخدام children() للحصول على العناصر الفرعية
        aChildren = oCell.children()
        
        # البحث عن العناصر وتحديثها
        for child in aChildren {
            if child.objectName() = "cellType"
                oCellType = child
            but child.objectName() = "inputCell"
                oInput = child
            ok
        }
        
        if oCellType != NULL and oInput != NULL
            cType = oCellType.currenttext()
            
            # تحديث نوع الخلية في القائمة
            aCells[nIndex][5] = lower(cType)
            
            # تحديث مظهر منطقة الإدخال
            if cType = "نص"
                oInput.setStyleSheet("
                    QPlainTextEdit {
                        border: none;
                        padding: 10px;
                        font-family: 'Arial';
                    }
                ")
            else
                oInput.setStyleSheet("
                    QPlainTextEdit {
                        border: none;
                        padding: 10px;
                        font-family: 'Courier New';
                    }
                ")
            ok
        ok
    ok

# دالة تحريك الخلية للأسفل
func moveCellDown nIndex
    see "moveCellDown index: " + nIndex + nl
    see "aCells length: " + len(aCells) + nl
    
    try {
        if nIndex >= 1 and nIndex < len(aCells)
            # تبديل الخلايا في المصفوفة
            temp = aCells[nIndex]
            aCells[nIndex] = aCells[nIndex + 1]
            aCells[nIndex + 1] = temp
            
            # إعادة ترتيب الخلايا في الواجهة
            for i = 1 to len(aCells)
                if aCells[i][1] != NULL
                    oNotebookEditorLayout.removeWidget(aCells[i][1])
                ok
            next
            
            for i = 1 to len(aCells)
                if aCells[i][1] != NULL
                    oNotebookEditorLayout.addWidget(aCells[i][1])
                ok
            next
            
            # تحديث أرقام الخلايا
            updateCellNumbers()
        ok
    catch
        see "Error in moveCellDown: " + cCatchError + nl
    }

# دالة تحريك الخلية للأعلى
func moveCellUp nIndex
    see "moveCellUp index: " + nIndex + nl
    see "aCells length: " + len(aCells) + nl
    
    try {
        if nIndex > 1 and nIndex <= len(aCells)
            # تبديل الخلايا في المصفوفة
            temp = aCells[nIndex]
            aCells[nIndex] = aCells[nIndex - 1]
            aCells[nIndex - 1] = temp
            
            # إعادة ترتيب الخلايا في الواجهة
            for i = 1 to len(aCells)
                if aCells[i][1] != NULL
                    oNotebookEditorLayout.removeWidget(aCells[i][1])
                ok
            next
            
            for i = 1 to len(aCells)
                if aCells[i][1] != NULL
                    oNotebookEditorLayout.addWidget(aCells[i][1])
                ok
            next
            
            # تحديث أرقام الخلايا
            updateCellNumbers()
        ok
    catch
        see "Error in moveCellUp: " + cCatchError + nl
    }

# دالة تحديث أرقام الخلايا
func updateCellNumbers
    try {
        see "updateCellNumbers: length = " + len(aCells) + nl
        for i = 1 to len(aCells)
            oCell = aCells[i][1]
            if oCell != NULL
                aChildren = oCell.children()
                for child in aChildren {
                    if child.objectName() = "cellLabel"
                        child.setText("خلية [" + i + "]")
                        exit
                    ok
                }
                # تحديث رقم الخلية في المصفوفة
                aCells[i][4] = i
            ok
        next
    catch
        see "Error in updateCellNumbers: " + cCatchError + nl
    }

# دالة حذف الخلية
func deleteCell nIndex
    see "deleteCell index: " + nIndex + nl
    see "aCells length before: " + len(aCells) + nl
    if nIndex >= 1 and nIndex <= len(aCells)
        try {
            oCell = aCells[nIndex][1]
            if oCell != NULL {
                oLayout = oNotebookEditor.layout()
                if oLayout != NULL {
                    oLayout.removeWidget(oCell)
                    oCell.hide()
                    oCell.delete()
                }
            }
            del(aCells, nIndex)
            see "aCells length after: " + len(aCells) + nl
            updateCellNumbers()
            status1.showmessage("تم حذف الخلية", 0)
        catch
            see "Error in deleteCell: " + cCatchError + nl
        }
    ok

# دالة إضافة خلية جديدة
func addNewCell
    try {
        nCellNumber = len(aCells) + 1
        see "Adding new cell number: " + nCellNumber + nl
        oCell = createNewCell(nCellNumber)
        if oNotebookEditorLayout != NULL {
            oNotebookEditorLayout.addWidget(oCell)
            status1.showmessage("تم إضافة خلية جديدة", 0)
        else
            see "Error: oNotebookEditorLayout is NULL" + nl
        }
    catch
        see "Error in addNewCell: " + cCatchError + nl
    }

# دالة حفظ النوتبوك
func saveNotebook
    new qfiledialog(win1) {
        cName = getsavefilename(win1,"حفظ النوتبوك","","Notebook Files(*.rnb)")
        if cName != NULL
            cContent = ""
            for cell in aCells
                oCell = cell[1]
                aChildren = oCell.children()
                cInput = ""
                
                # البحث عن محتوى الإدخال
                for child in aChildren {
                    if child.objectName() = "inputCell"
                        cInput = child.toPlainText()
                        exit
                    ok
                }
                
                cContent += cInput + "|--|" + cell[3] + "§§§"
            next
            write(cName, cContent)
            status1.showmessage("تم حفظ النوتبوك: " + cName, 0)
        ok
    }

# دالة فتح النوتبوك
func openNotebook
    new qfiledialog(win1) {
        cName = getopenfilename(win1,"فتح نوتبوك","","Notebook Files(*.rnb)")
        if cName != NULL
            # مسح الخلايا الحالية
            for cell in aCells
                cell[1].hide()
                cell[1].delete()
            next
            aCells = []
            
            # قراءة محتوى الملف
            cContent = read(cName)
            aCellsContent = split(cContent, "§§§")
            
            # إنشاء الخلايا
            for cCellContent in aCellsContent
                if len(cCellContent) > 0
                    aParts = split(cCellContent, "|--|")
                    if len(aParts) >= 2
                        oCell = createNewCell(len(aCells) + 1)
                        aChildren = oCell.children()
                        
                        # تحديث محتوى الإدخال والإخراج
                        for child in aChildren {
                            if child.objectName() = "inputCell"
                                child.setPlainText(aParts[1])
                            but child.objectName() = "outputCell" and len(aParts[2]) > 0
                                child.setPlainText(aParts[2])
                                child.show()
                            ok
                        }
                        
                        oLayout = oNotebookEditor.layout()
                        if oLayout != NULL
                            oLayout.addWidget(oCell)
                        ok
                    ok
                ok
            next
            
            status1.showmessage("تم فتح النوتبوك: " + cName, 0)
        ok
    }

# دالة تشغيل جميع الخلايا
func runAllCells
    for i = 1 to len(aCells)
        runCell(i)
    next
    status1.showmessage("تم تشغيل جميع الخلايا", 0)

# دالة تشغيل الخلية
func runCell nIndex
    see "runCell index: " + nIndex + nl
    see "aCells length: " + len(aCells) + nl

    try {
        if nIndex >= 1 and nIndex <= len(aCells)
            oCell = aCells[nIndex][1]
            if oCell != NULL
                aChildren = oCell.children()
                oInput = NULL
                oOutput = NULL
                
                for child in aChildren {
                    if child.objectName() = "inputCell"
                        oInput = child
                    but child.objectName() = "outputCell"
                        oOutput = child
                    ok
                }
                
                if oInput != NULL {
                    cCode = oInput.toPlainText()
                    aCells[nIndex][2] = cCode  # حفظ النص
                    
                    # التعامل مع الخلية حسب نوعها
                    if aCells[nIndex][5] = "code"
                        if len(cCode) > 0 {
                            try {
                                eval(cCode)
                                if oOutput != NULL {
                                    oOutput.setPlainText(cOutput)
                                    aCells[nIndex][3] = cOutput  # حفظ النتيجة
                                    oOutput.show()
                                }
                            catch
                                if oOutput != NULL {
                                    oOutput.setPlainText("خطأ: " + cCatchError)
                                    aCells[nIndex][3] = "خطأ: " + cCatchError
                                    oOutput.show()
                                }
                            }
                        }
                    else  # نص عادي
                        if oOutput != NULL {
                            oOutput.setPlainText(cCode)
                            aCells[nIndex][3] = cCode
                            oOutput.show()
                        }
                    ok
                }
            ok
        ok
    catch
        see "Error in runCell: " + cCatchError + nl
    }

/*
الدالة: cleanTextForJson
الوصف: تنظيف النص من العلامات التي تؤثر على JSON
المدخلات: cText - النص المراد تنظيفه
المخرجات: النص بعد التنظيف
*/
func cleanTextFromAssistant cText
    try {
        # محاولة تحليل النص كـ JSON مباشرة
        assistantCleanText = Json2list(cText)
        return cText
    catch
        # إذا فشل التحليل، نقوم بتنظيف النص
        cResult = cText
        
        # البحث عن بداية ونهاية JSON
        nStartJson = substr(cResult, "{")
        if nStartJson > 0
            # البحث عن آخر } في النص
            nLastBrace = 0
            nPos = 1
            while true
                nPos = substr(cResult, "}", nPos)
                if nPos = 0 break ok
                nLastBrace = nPos
                nPos++
            end
            
            if nLastBrace > 0
                cResult = substr(cResult, nStartJson, nLastBrace)
            ok
        ok
        
        # تنظيف علامات التنصيص المزدوجة
        cResult = substr(cResult, '\\"', '"')
        
        # تنظيف السطور الجديدة والمسافات
        cResult = substr(cResult, "\n", " ")
        cResult = substr(cResult, "\r", "")
        cResult = substr(cResult, "\t", " ")
        
        return cResult
    }

func prepareTextToAssistant cText
    cResult = cText
    # تنظيف علامات التنصيص
    cResult = substr(cResult, '"', '\"')
    cResult = substr(cResult, nl, "\n")
    cResult = substr(cResult, "\t", "\\t")
    cResult = substr(cResult, "\\", "\\\\")
    cResult = substr(cResult, "'", "\'")
    # تنظيف الأكواد البرمجية
    if substr(cResult, "```")
        cResult = substr(cResult, "```", "\`\`\`")
    ok
    return cResult
