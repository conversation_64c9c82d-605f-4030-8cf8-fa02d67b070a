@echo off
echo تشغيل محرر مايسترو الذكي...
echo ================================

REM التحقق من وجود Ring
where ring >nul 2>nul
if %errorlevel% neq 0 (
    echo خطأ: لم يتم العثور على Ring Programming Language
    echo يرجى تثبيت Ring أولاً من: http://ring-lang.net
    pause
    exit /b 1
)

REM التحقق من وجود الملفات المطلوبة
if not exist "smart_editor.ring" (
    echo خطأ: لم يتم العثور على ملف smart_editor.ring
    pause
    exit /b 1
)

if not exist "maestro_prompt.txt" (
    echo تحذير: لم يتم العثور على ملف maestro_prompt.txt
    echo سيتم إنشاؤه تلقائياً...
)

echo تشغيل المحرر...
ring smart_editor.ring

pause
