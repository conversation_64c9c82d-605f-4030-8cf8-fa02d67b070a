# ملف اختبار لمحرر مايسترو
# يمكنك استخدام هذا الملف لاختبار الميزات الجديدة



sayHello("أحمد")

oPerson = new Person("سارة", 25)
oPerson.introduce()

# جرب تشغيل هذا لرؤية رابط المساعدة
 buggyFunction()


# دالة بسيطة لاختبار الشرح
func sayHello cName
    ? "مرحبا " + cName
    return "تم الترحيب بـ " + cName

# كلاس بسيط لاختبار إعادة الهيكلة
class Person
    cName = ""
    nAge = 0
    
    func init cPersonName, nPersonAge
        cName = cPersonName
        nAge = nPersonAge
    
    func introduce
        ? "اسمي " + cName + " وعمري " + nAge + " سنة"

# كود به خطأ لاختبار التصحيح
func buggyFunction
    nResult = 10 / 0  # خطأ: القسمة على صفر
    return nResult

# استدعاء الدوال